﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.UI.Main.Pages.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class CalendarResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CalendarResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.UI.Main.Pages.Resources.CalendarResource", typeof(CalendarResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy.
        /// </summary>
        public static string CopyEvent {
            get {
                return ResourceManager.GetString("CopyEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cut.
        /// </summary>
        public static string CutEvent {
            get {
                return ResourceManager.GetString("CutEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string DeleteEvent {
            get {
                return ResourceManager.GetString("DeleteEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate.
        /// </summary>
        public static string DuplicateEvent {
            get {
                return ResourceManager.GetString("DuplicateEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        public static string EditEvent {
            get {
                return ResourceManager.GetString("EditEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event.
        /// </summary>
        public static string Event {
            get {
                return ResourceManager.GetString("Event", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event does not exists. Sync data to update..
        /// </summary>
        public static string EventNotExists {
            get {
                return ResourceManager.GetString("EventNotExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste is not allowed at this point.
        /// </summary>
        public static string EventPasteNotAllowedHere {
            get {
                return ResourceManager.GetString("EventPasteNotAllowedHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The maximum users selected..
        /// </summary>
        public static string MaximumCalendarVisisbleUsersSelected {
            get {
                return ResourceManager.GetString("MaximumCalendarVisisbleUsersSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        public static string NewEvent {
            get {
                return ResourceManager.GetString("NewEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        public static string NewEventBtn_Text {
            get {
                return ResourceManager.GetString("NewEventBtn_Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (no user).
        /// </summary>
        public static string NoneUser {
            get {
                return ResourceManager.GetString("NoneUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is nothing to paste..
        /// </summary>
        public static string NothingToPaste {
            get {
                return ResourceManager.GetString("NothingToPaste", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No users found.
        /// </summary>
        public static string NoUsersFound {
            get {
                return ResourceManager.GetString("NoUsersFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste.
        /// </summary>
        public static string PasteEvent {
            get {
                return ResourceManager.GetString("PasteEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        public static string PrintCalendarTooltip {
            get {
                return ResourceManager.GetString("PrintCalendarTooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calendar.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
    }
}
