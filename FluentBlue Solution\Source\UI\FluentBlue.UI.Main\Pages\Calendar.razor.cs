using AKSoftware.Blazor.Utilities;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Components;
using FluentBlue.UI.Main.Layout;
using FluentBlue.UI.Main.Services;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.EntityFrameworkCore.ValueGeneration;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor.Navigations;
using Syncfusion.Blazor.Popups;
using Syncfusion.Blazor.Schedule;
using System.Collections.ObjectModel;
using System.Dynamic;

namespace FluentBlue.UI.Main.Pages
{
    public partial class Calendar
    {
        //General
        bool loading = false;  //Indicates if data are loading.
        bool dragging = false;  //Indicates if user is dragging an event.
        IDialogReference? dialog;
        bool pageLoaded = false;
        private bool hasRenderedOnce = false;
        UserSetting? userSetting = null;
        private bool prerequisiteDataInitialized = false;
        private bool shouldRender = false;

        //Calendar
        Syncfusion.Blazor.Schedule.SfSchedule<Data.Model.DBOs.Calendar.Event> schedule = new Syncfusion.Blazor.Schedule.SfSchedule<Data.Model.DBOs.Calendar.Event>();
        DateTime CurrentDate = DateTime.Today;
        List<Data.Model.DBOs.Calendar.Event> events = new List<Data.Model.DBOs.Calendar.Event>();
        List<Data.Model.DTOs.UserLI>? users = null; //new List<Data.Model.DTOs.UserLI>();
        public string[] resourcesGroup { get; set; } = { "Users" };
        public string resourceColor { get; set; } = "Categories";
        List<EventCategory> eventCategories = new List<EventCategory>();
        bool enableAdaptiveUI = false;
        bool menuClickedCell = false;  //Identifies if user right clicked in a cell.
        bool menuClickedEvent = false;  //Identifies if user right clicked in an event.
        bool menuClickedRecurrentEvent = false;  //Identifies if user right clicked in a recurrent event.
        SfContextMenu<MenuItem> calendarContextMenu = new SfContextMenu<MenuItem>();

        //Οι παρακάτω 3 μεταβλητές σχετίζονται με το δεξί κλικ μενού.
        private Event? contextMenuEvent = null;  //Το Event στο οποίο έγινε το δεξί κλικ.
        private CellClickEventArgs? contextMenuCell = null;  //Πληροφορίες σχετικά με το δεξί κλικ (μενού) στο ημερολόγιο.
        private UserLI? contextMenuUser = null;  //Περιέχει το UserLI δηλαδή το User group στου οποίου πατήθηκε το δεξί κλικ στο ημερολόγιο.
        private Guid? cutEventId = null;  //Αν το cutEventId είναι null τότε δεν έχει γίνει cut, αν έχει τιμή τότε έχει γίνει cut και περιμένει paste.

        int? initialGroupIndexWhileDragging = null;
        int? finalGroupIndexWhileDragging = null;
        UserLI? newUserWhileDragging = null;

        //Variables for schedule settings
        private string workStartHour = "", workEndHour = string.Empty;
        private int interval = 60, slotsCount = 4;
        private string timeZone = string.Empty;

        private bool schedulerInitialized = false;
        private Dictionary<string, string> eventCategoriesColors = new Dictionary<string, string>();  //Dictionary χρώματος ενός EventCategory με EventCategoryId για να χρησιμοποιηθεί για γρήγορη αναζήτηση.
        IEnumerable<UserLI> displayUsersDS = new List<UserLI>().AsEnumerable();  //Οι Users που θα μπουν ως DataSource στο dropdown των visible Users
        IEnumerable<UserLI> selectedDisplayUsers = new List<UserLI>().AsEnumerable();  //Οι Users που έχει επιλέξει ο χρήστης στο dropdown των visible Users (αυτοί που θα εμφανίζονται στο ημερολόγιο).
        ObservableCollection<UserLI> displayedUsersInScheduler = new ObservableCollection<UserLI>();  //Οι Users που θα εμφανίζει το scheduler.
        FluentAutocomplete<UserLI> displayUsersDropDown = default!;

        protected override bool ShouldRender()
        {
            bool tempShouldRender = this.shouldRender;
            this.shouldRender = false;
            return tempShouldRender;
        }

        protected void StateHasChangedOptimized()
        {
            this.shouldRender = true;
            StateHasChanged();
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                //if(formFactor.GetPlatform() == "Web")
                //{
                //    this.rend(new InteractiveWebAssemblyRenderMode(false))
                //}

                //#region  Κανονικά ο κώδικας αυτός γίνεται στο OnAfterRenderAsync, στη περίπτωση που έχουμε τα prerequisite data κάνει intialized το scheduler για να εμφανίζεται καλύτερο το rendering.
                //#region  Διαβάζει τα UserSettings μόνο από το cache.
                //var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };
                //this.userSetting = await cache.get(Keywords.UserSetting,
                //    async cancel =>
                //    {
                //        return await Task.FromResult<UserSetting>(null);
                //    });
                //#endregion

                //#region Διαβάζει τους Users μόνο από το cache.
                //var usersCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(60) };
                //this.users = await cache.GetOrCreateAsync(
                //    Keywords.Users,
                //    async cancel =>
                //    {
                //        return await Task.FromResult<List<Data.Model.DTOs.UserLI>>(null);
                //    },
                //    usersCacheOptions,
                //    new[] { "Deletable" });
                //#endregion

                //if (this.userSetting !=null && this.users!=null)
                //{
                //    await this.InitializeScheduler();
                //}
                //#endregion

                MessagingCenter.Subscribe<SettingsDialog>(this, Keywords.SettingsUpdated, async (sender) =>
                {
                    try
                    {
                        //await this.SetScheduleSettings();
                        //await this.InitializeCalendarDisplayUsers();
                        #region  Διαβάζει ξανά τα UserSettings που αποθηκεύτηκε στα Settings.
                        var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };
                        this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                            async cancel =>
                            {
                                UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                                return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                            }, userSettingCacheOptions);
                        #endregion
                        await this.InvokeAsync(async () => await this.LoadEvents(true));
                    }
                    catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
                    {
                        new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
                    }
                    catch (ApplicationException ex)
                    {
                        logger.LogError(ex, ex.Message);
                        new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, ex.Message);
                        new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                    }
                });

                MessagingCenter.Subscribe<MainLayout>(this, Keywords.ReloadEvents, async (sender) =>
                {
                    try
                    {
                        int repeat = 6;
                        while ((this.loading || this.dragging) && repeat > 0)
                        {
                            await Task.Delay(1000);
                            repeat--;
                        }

                        await InvokeAsync(async () => { await LoadEvents(); });
                        //this.StateHasChanged();
                    }
                    catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
                    {
                        new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
                    }
                    catch (ApplicationException ex)
                    {
                        logger.LogError(ex, ex.Message);
                        new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, ex.Message);
                        new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                    }
                });

                //#region  Διαβάζει τα UserSettings
                //var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                //this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                //    async cancel =>
                //    {
                //        UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                //        return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                //    }, userSettingCacheOptions);
                //#endregion

                //await this.SetScheduleSettings();
                //await this.InitializeCalendarDisplayUsers();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    // Remove the cut event from the cache
                    await cache.RemoveAsync("CutCalendarEvent");

                    #region  Διαβάζει τα UserSettings γιατί παραπάνω με τα async, μπορεί η OnInitialized να μην έχει ολοκληρωθει και να φτάσει εδώ ο κώδικας.
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };
                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);
                    #endregion

                    #region Διαβάζει τους Users.
                    var usersCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(60) };
                    this.users = await cache.GetOrCreateAsync(
                        Keywords.Users,
                        async cancel =>
                        {
                            Task<List<Data.Model.DTOs.UserLI>> usersTask = new WebApi.Client.UsersWebApiClient(httpClient, usersWebApiClientLogger).GetUsersList(AuthenticatedUserData.TenantId);
                            var tempUsers = await usersTask;
                            //TODO: Να προσθέσω ρύθμιση σε όλη την εφαρμογή για το αν ο χρήστης θέλει να εμφανίζεται σαν επιλογή και ο "κανενας" χρήστης στο ημερολόγιο.
                            //if (userSettings.AllowNullUserInCalendar == true)
                            //{
                            //    UserLI emptyUser = UserLI.Empty;
                            //    emptyUser.UserId = Guid.Parse("11111111-1111-1111-1111-111111111111");  //Βάζουμε 1111...-111 γιατί με το Guid.Emtpy που είναι μηδενικά δεν δουλεύει.
                            //    emptyUser.FullName = Resources.CalendarResource.NoneUser;
                            //    tempUsers.Insert(0, emptyUser);  //Προσθέτει το κενό User για να χρησιμοποίησει τον Scheduler ως κανένας User.
                            //}
                            return tempUsers;
                        },
                        usersCacheOptions,
                        new[] { "Deletable" });
                    #endregion

                    await InitDisplayUsersDropDown();
                    await this.InitializeScheduler();
                    this.prerequisiteDataInitialized = true;

                    await InvokeAsync(async () => { await LoadEvents(); });

                    if (this.hasRenderedOnce == false)
                    {
                        this.hasRenderedOnce = true;
                    }

                    await this.schedule.ScrollToAsync(userSetting!.CalendarScrollToTime.GetBusinessValue());

                    this.pageLoaded = true;
                    this.StateHasChangedOptimized();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task InitDisplayUsersDropDown()
        {
            if (this.users == null)
            {
                return;
            }

            //Ρυθμίζει το DataSource του displayUsersDropDown
            List<Data.Model.DTOs.UserLI> tempDisplayUsers = this.users.Where(x => x.AvailableInEventUsers).ToList();  //Βρίσκει μόνο τους Users που μπορούν να εμφανίζονται στο ημερολόγιο.
            this.displayUsersDS = tempDisplayUsers.AsEnumerable();

            #region Ρυθμίζει τα SelectedItems του DropDown με τους Users που πρέπει να είναι ανοιχτά (διαβάζοντας από το cache)
            //Ψάχνει αρχικά στο cache.
            HybridCacheEntryOptions lastSelectedDisplayUsersCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };//Δεν θέλουμε να καθαρίσει όσο η εφαρμογή τρέχει.
            List<Data.Model.DTOs.UserLI>? lastSelectedDisplayUsers = await cache.GetOrCreateAsync<List<Data.Model.DTOs.UserLI>?>(
                Keywords.LastSelectedCalendarDisplayUsers,
                async cancel => await Task.FromResult<List<Data.Model.DTOs.UserLI>?>(null),
                lastSelectedDisplayUsersCacheOptions);

            displayedUsersInScheduler.Clear();

            //Αν βρήκε στο cache του πρόσφατους επιλεγμένους Users που εμφανίζονται στο ημερολόγιο.
            if (lastSelectedDisplayUsers?.Count > 0)
            {
                foreach (Data.Model.DTOs.UserLI lastSelectedCalendarDisplayUser in lastSelectedDisplayUsers.ToList())
                {
                    if (this.displayUsersDS.Where(u => u.UserId == lastSelectedCalendarDisplayUser.UserId).Any())
                    {
                        //ΠΡΟΣΟΧΗ: τα UserLI που βάζουμε στο displayedUsersInScheduler και στο selectedDisplayUsers πρέπει να προέρχονται πάντα από το displayUsersDS.
                        displayedUsersInScheduler.Add(this.displayUsersDS.Where(x => x.UserId == lastSelectedCalendarDisplayUser.UserId).FirstOrDefault()!);
                    }
                }
                this.selectedDisplayUsers = this.displayedUsersInScheduler;
                //this.selectedDisplayUsers = displayedUsersInScheduler.AsEnumerable();
                await cache.RemoveAsync(Keywords.LastSelectedCalendarDisplayUsers);  //Καθαρίζει το cache γιατί το χρησιμοποιήσαμε.
                await cache.SetAsync(Keywords.LastSelectedCalendarDisplayUsers, displayedUsersInScheduler.ToList());  //Αποθηκεύει το cache με τους επιλεγμένους display Users που εμφανίζονται στο ημερολόγιο.
            }
            else  //Αν δεν βρήκε στο HybridCache πρόσφατους επιλεγμένους Users (από το χρήστη) που εμφανίζονται στο ημερολόγιο.
            {
                UserLI? userLI = this.displayUsersDS.FirstOrDefault();  //Εδώ θα πρέπει πάντα να υπάρχει τουλάχιστον ένας User που να επιτρέπεται να εμφανίζεται στο ημερολόγιο.
                displayedUsersInScheduler.Clear();
                displayedUsersInScheduler.Add(userLI!);
                this.selectedDisplayUsers = this.displayedUsersInScheduler;
            }
            #endregion

            this.StateHasChangedOptimized();
        }

        private async Task LoadEvents(bool initializeScheduler = false)
        {
            try
            {
                //this.events = new List<Event>();
                //this.users = new List<UserLI>();
                //this.selectedDisplayUsers = new List<UserLI>().AsEnumerable();
                //await this.schedule.RefreshEventsAsync();
                //this.schedule.GetResourceCollections()[0].DataSource=null;  //Αν δεν το βάλω, δεν εμφανίζει τους Users στο ημερολόγιο.

                //this.schedule.RemoveResource(new List<Guid>(), "Users");

                // Ensure the application instance ID is included in the resource to prevent conflicts
                //this.schedule.ResourceIdField = "UserId";
                //this.schedule.ResourceGroupField = ApplicationInstanceInfo.ApplicationInstanceId.ToString();

                //#region  Διαβάζει ξανά τα UserSettings.
                //var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };
                //this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                //    async cancel =>
                //    {
                //        UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                //        return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                //    }, userSettingCacheOptions);
                //#endregion

                //#region Διαβάζει τους Users.
                //var usersCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(60) };
                //this.users = await cache.GetOrCreateAsync(
                //    Keywords.Users,
                //    async cancel =>
                //    {
                //        Task<List<Data.Model.DTOs.UserLI>> usersTask = new WebApi.Client.UsersWebApiClient(httpClient, usersWebApiClientLogger).GetUsersList(AuthenticatedUserData.TenantId);
                //        var tempUsers = await usersTask;
                //        //TODO: Να προσθέσω ρύθμιση σε όλη την εφαρμογή για το αν ο χρήστης θέλει να εμφανίζεται σαν επιλογή και ο "κανενας" χρήστης στο ημερολόγιο.
                //        //if (userSettings.AllowNullUserInCalendar == true)
                //        //{
                //        //    UserLI emptyUser = UserLI.Empty;
                //        //    emptyUser.UserId = Guid.Parse("11111111-1111-1111-1111-111111111111");  //Βάζουμε 1111...-111 γιατί με το Guid.Emtpy που είναι μηδενικά δεν δουλεύει.
                //        //    emptyUser.FullName = Resources.CalendarResource.NoneUser;
                //        //    tempUsers.Insert(0, emptyUser);  //Προσθέτει το κενό User για να χρησιμοποίησει τον Scheduler ως κανένας User.
                //        //}
                //        return tempUsers;
                //    },
                //    usersCacheOptions,
                //    new[] { "Deletable" });
                //#endregion


                if (this.schedulerInitialized == false || initializeScheduler == true)
                {
                    this.schedulerInitialized = true;

                    await InitializeScheduler();
                }


                //#region  Ρυθμίζει το selectedUsersInDropDown 
                //if (preventDisplayUsers == false)
                //{
                //    #region Ρυθμίζει το DataSource του selectedUsersInDropDown
                //    if (this.users != null)
                //    {
                //        //Βρίσκει μόνο τους Users που μπορούν να εμφανίζονται στο ημερολόγιο.
                //        List<Data.Model.DTOs.UserLI> usersForCalendar = this.users.Where(x => x.AvailableInEventUsers).ToList();

                //        this.displayUsersDS = usersForCalendar.AsEnumerable();
                //    }
                //    #endregion

                //    #region Ρυθμίζει τα SelectedItems του selectedUsersInDropDown με τους Users που πρέπει να είναι ανοιχτά (διαβάζοντας από το cache)
                //    //Ψάχνει αρχικά στο cache.
                //    HybridCacheEntryOptions lastSelectedCalendarDisplayUsersCacheOptions = new HybridCacheEntryOptions
                //    {
                //        LocalCacheExpiration = TimeSpan.FromHours(100)  //Δεν θέλουμε να καθαρίσει όσο η εφαρμογή τρέχει.
                //    };
                //    List<Data.Model.DTOs.UserLI>? lastSelectedCalendarDisplayUsers = await cache.GetOrCreateAsync<List<Data.Model.DTOs.UserLI>?>(
                //        Keywords.LastSelectedCalendarDisplayUsers,
                //        async cancel => await Task.FromResult<List<Data.Model.DTOs.UserLI>?>(null),
                //        lastSelectedCalendarDisplayUsersCacheOptions);

                //    //Αν βρήκε στο cache του πρόσφατους επιλεγμένους Users που εμφανίζονται στο ημερολόγιο.
                //    if (lastSelectedCalendarDisplayUsers?.Count > 0)
                //    {
                //        //Checks if lastSelectedCalendarDisplayUsers exists in users, and if not, remove it.
                //        foreach (Data.Model.DTOs.UserLI lastSelectedCalendarDisplayUser in lastSelectedCalendarDisplayUsers.ToList())
                //        {
                //            if (this.users?.Where(u => u.UserId == lastSelectedCalendarDisplayUser.UserId).FirstOrDefault() == null)
                //            {
                //                lastSelectedCalendarDisplayUsers.Remove(lastSelectedCalendarDisplayUser);
                //            }
                //        }


                //        this.displayedUsersInScheduler = this.users?.Where(u => lastSelectedCalendarDisplayUsers.Any(lsu => lsu.UserId == u.UserId)).ToList().AsEnumerable() ?? new List<UserLI>().AsEnumerable();
                //        this.selectedUsersInDropDown = this.displayedUsersInScheduler;
                //    }
                //    else  //Αν δεν βρήκε στο HybridCache πρόσφατους επιλεγμένους Users (από το χρήστη) που εμφανίζονται στο ημερολόγιο.
                //    {
                //        UserLI? userLI = this.users?.Where(x => x.AvailableInEventUsers == true).FirstOrDefault();  //Εδώ θα πρέπει πάντα να υπάρχει τουλάχιστον ένας User που να επιτρέπεται να εμφανίζεται στο ημερολόγιο.
                //        List<UserLI> selectedUsers = new List<UserLI>() { userLI! };
                //        this.displayedUsersInScheduler = selectedUsers.AsEnumerable();
                //        //this.displayUsersDropDown.SelectedOptions = this.schedulerDisplayUsers;
                //        this.selectedUsersInDropDown = this.displayedUsersInScheduler;
                //        //this.selectedDisplayUsers2 = selectedUsers.ToList();
                //    }
                //    #endregion
                //}
                //else
                //{
                //    this.displayedUsersInScheduler = this.selectedUsersInDropDown;
                //}
                //#endregion




                //TODO: H FillSchedule τρέχει 2 φορές, να φτιαχτεί να τρέχει μια.
                this.loading = true;

                #region Διαβάζει τα EventCategories.
                HybridCacheEntryOptions eventCategoriesCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(60) };
                this.eventCategories = await cache.GetOrCreateAsync(
                    Keywords.EventCategories,
                    async cancel =>
                    {
                        EventCategoriesWebApiClient eventCategoriesWebApiClient = new EventCategoriesWebApiClient(httpClient, eventCategoriesWebApiClientLogger);
                        List<Data.Model.DBOs.Calendar.EventCategory>? tempEventCategories = await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);

                        ////Δημιουργεί το Dictionary με τα χρώματα.
                        //this.eventCategoriesColors.Clear();
                        //foreach (EventCategory eventCategory in tempEventCategories)
                        //{
                        //    this.eventCategoriesColors.Add(eventCategory.EventCategoryId.ToString(), eventCategory.ColorHex);
                        //}

                        return tempEventCategories;
                    },
                    eventCategoriesCacheOptions,
                    new[] { "Deletable" });
                #endregion

                #region  Δημιουργεί το array με τα χρώματα των EventCategories (για πιο γρήγορη αναζήτηση).
                this.eventCategoriesColors.Clear();  //Το καθαρίζει για να μην προστεθούν 2η φορά τα ίδια EventCategories, βγάζει σφάλμα.

                // Add a default entry for empty or null EventCategoryId
                this.eventCategoriesColors.Add("", "#FFFFFF");

                foreach (Data.Model.DBOs.Calendar.EventCategory eventCategory in this.eventCategories)
                {
                    this.eventCategoriesColors.Add(eventCategory.EventCategoryId.ToString(), eventCategory.BackColor);
                }
                #endregion

                #region  Διαβάζει τα Events.
                DateTime minDate = this.schedule.GetCurrentViewDates().Min().AddDays(-1);  //Αφαιρεί μια ημέρα για να καλύψει τη ανακρίβεια ώρας λόγω UTC.
                DateTime maxDate = this.schedule.GetCurrentViewDates().Max().AddDays(1);  //Προσθέτει μια ημέρα για να καλύψει τη ανακρίβεια ώρας λόγω UTC.
                Guid[]? visibleUsersGuids = null;
                if (this.displayedUsersInScheduler.Count() > 0)
                {
                    visibleUsersGuids = this.displayedUsersInScheduler.Where(x => x.UserId != null).Select(x => x.UserId!.Value).ToArray();
                }

                Task<List<Data.Model.DBOs.Calendar.Event>> eventsTask = new WebApi.Client.EventsWebApiClient(httpClient, eventsWebApiClientLogger).GetEvents(AuthenticatedUserData.TenantId, "", visibleUsersGuids, minDate, maxDate);
                List<Data.Model.DBOs.Calendar.Event> tempEvents = await eventsTask;

                //Ρυθμίζει το TimeZone στα Events.
                foreach (var e in tempEvents)
                {
                    e.UserTimeZoneId = userSetting!.TimeZone;
                }

                #region  Πριν δώσει τα tempEvents στο Scheduler ελέγχει αν έχει όλα τα EventCategories.
                // Foreach Event in tempEvents, checks if EventCategoryId does not exist in eventCategories.
                bool missingEventCategoryFound = false;
                foreach (var evt in tempEvents)
                {
                    if (!this.eventCategories.Any(ec => ec.EventCategoryId == evt.EventCategoryId))
                    {
                        missingEventCategoryFound = true;
                    }
                }
                if (missingEventCategoryFound)
                {
                    //Επαναλαμβάνει ξανά τη διαδικασία για τα EventCategories, την οποία έκανε παραπάνω.
                    EventCategoriesWebApiClient eventCategoriesWebApiClient = new EventCategoriesWebApiClient(httpClient, eventCategoriesWebApiClientLogger);
                    List<Data.Model.DBOs.Calendar.EventCategory>? tempEventCategories = await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);
                    tempEventCategories.Insert(0, new EventCategory() { EventCategoryId = Guid.Empty, Name = "", BackColor = "" });  //Προσθέτει το κενό EventCategory (αντιπροσωπεύει το null).

                    await cache.SetAsync(Keywords.EventCategories, tempEventCategories);
                    this.eventCategories = tempEventCategories;

                    #region  Δημιουργεί το array με τα χρώματα των EventCategories (για πιο γρήγορη αναζήτηση).
                    this.eventCategoriesColors.Clear();  //Το καθαρίζει για να μην προστεθούν 2η φορά τα ίδια EventCategories, βγάζει σφάλμα.

                    // Add a default entry for empty or null EventCategoryId
                    this.eventCategoriesColors.Add("", "#FFFFFF");

                    foreach (Data.Model.DBOs.Calendar.EventCategory eventCategory in this.eventCategories)
                    {
                        this.eventCategoriesColors.Add(eventCategory.EventCategoryId.ToString(), eventCategory.BackColor);
                    }
                    #endregion
                }
                #endregion

                this.events.Clear();
                this.events = tempEvents;
                #endregion



                //List<ScheduleResource<Event, UserLI>> resourceData = new()
                //{
                //    new ScheduleResource<Event, UserLI>
                //    {
                //        Field = "EventUserIds",
                //        Title = "User",
                //        Name = "Users",
                //        AllowMultiple = true,
                //        DataSource = this.selectedDisplayUsers,
                //        TextField = "FullName",
                //        IdField = "UserId"
                //    }
                //};

                // Set the resource data to the scheduler
                //this.schedule.AddResource(resourceData, "Users", 0);
                await this.schedule.RefreshEventsAsync(true);  //Refreshes the events in the calendar.

                StateHasChangedOptimized();
            }
            catch (Exception ex)
            {
                this.loading = false;
                logger.LogError(ex, ex.Message);
                //new ErrorNotifier(dialogService).ShowError(GlobalResource.ErrorOccured, "");
                throw;
            }
            finally
            {
                this.loading = false;
            }


        }


        async Task InitializeScheduler()
        {
            if (formFactor.GetDeviceIdiom() == "Web")
            {
                this.enableAdaptiveUI = false;
            }
            else if (formFactor.GetDeviceIdiom() == "Phone")
            {
                this.enableAdaptiveUI = true;
            }

            this.workStartHour = userSetting!.CalendarWorkStart.ToString(@"hh\:mm");
            this.workEndHour = userSetting!.CalendarWorkEnd.ToString(@"hh\:mm");
            this.schedule.WorkDays = userSetting.CalendarWorkDays.Select(c => c - '0').ToArray();


            this.timeZone = userSetting!.TimeZone;

            if (this.userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.SixtyMinutes)
            {
                this.slotsCount = 1;
                this.interval = 60;
            }
            else if (this.userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.ThirtyMinutes)
            {
                this.slotsCount = 2;
                this.interval = 60;
            }
            else if (this.userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.TwentyMinutes)
            {
                this.slotsCount = 3;
                this.interval = 60;
            }
            else if (this.userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.FifteenMinutes)
            {
                this.slotsCount = 4;
                this.interval = 60;
            }
            else if (this.userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.TenMinutes)
            {
                this.slotsCount = 6;
                this.interval = 60;
            }
            else if (this.userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.FiveMinutes)
            {
                this.slotsCount = 6;
                this.interval = 30;
            }
            this.schedule.FirstDayOfWeek = (int)userSetting!.FirstDayOfWeek;

            #region  Ρυθμίζει το τελευταίο View που είχε ο χρήστης.
            HybridCacheEntryOptions calendarLastViewCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };
            View? view = await cache.GetOrCreateAsync<View>(Keywords.CalendarLastView,
                async cancel =>
                {
                    if (formFactor.GetDeviceIdiom() == "Web" || formFactor.GetDeviceIdiom() == "Desktop")
                    {
                        return await Task.FromResult(Enum.Parse<View>(this.userSetting.CalendarDefaultView.ToString()));
                    }
                    else if (formFactor.GetDeviceIdiom() == "Phone")
                    {
                        return await Task.FromResult(Enum.Parse<View>(this.userSetting.CalendarDefaultViewOnMobiles.ToString()));
                    }
                    else
                    {
                        return await Task.FromResult(View.Week);
                    }
                },
                calendarLastViewCacheOptions);

            if (view != null)  //Αν υπάρχει στο cache
            {
                await this.schedule.ChangeCurrentViewAsync(view.Value);
            }
            else
            {
                if (formFactor.GetDeviceIdiom() == "Web")
                {
                    await this.schedule.ChangeCurrentViewAsync(Enum.Parse<View>(userSetting.CalendarDefaultView.ToString()));
                }
                else if (formFactor.GetDeviceIdiom() == "Phone")
                {
                    await this.schedule.ChangeCurrentViewAsync(Enum.Parse<View>(userSetting.CalendarDefaultViewOnMobiles.ToString()));
                }
            }
            #endregion
        }

        private async Task LoadEvents2()
        {
            try
            {
                #region Διαβάζει τους Users.
                var usersCacheOptions = new HybridCacheEntryOptions
                {
                    LocalCacheExpiration = TimeSpan.FromMinutes(60)
                };

                this.users = await cache.GetOrCreateAsync(
                    Keywords.Users,
                    async cancel =>
                    {
                        Task<List<Data.Model.DTOs.UserLI>> usersTask = new WebApi.Client.UsersWebApiClient(httpClient, usersWebApiClientLogger).GetUsersList(AuthenticatedUserData.TenantId);
                        var tempUsers = await usersTask;
                        //TODO: Να προσθέσω ρύθμιση σε όλη την εφαρμογή για το αν ο χρήστης θέλει να εμφανίζεται σαν επιλογή και ο "κανενας" χρήστης στο ημερολόγιο.
                        //if (userSettings.AllowNullUserInCalendar == true)
                        //{
                        //    UserLI emptyUser = UserLI.Empty;
                        //    emptyUser.UserId = Guid.Parse("11111111-1111-1111-1111-111111111111");  //Βάζουμε 1111...-111 γιατί με το Guid.Emtpy που είναι μηδενικά δεν δουλεύει.
                        //    emptyUser.FullName = Resources.CalendarResource.NoneUser;
                        //    tempUsers.Insert(0, emptyUser);  //Προσθέτει το κενό User για να χρησιμοποίησει τον Scheduler ως κανένας User.
                        //}
                        return tempUsers;
                    },
                    usersCacheOptions,
                    new[] { "Deletable" });
                #endregion

                if (formFactor.GetDeviceIdiom() == "Web")
                {
                    this.enableAdaptiveUI = false;
                }
                else if (formFactor.GetDeviceIdiom() == "Phone")
                {
                    this.enableAdaptiveUI = true;
                }

                this.workStartHour = userSetting!.CalendarWorkStart.ToString(@"hh\:mm");
                this.workEndHour = userSetting!.CalendarWorkEnd.ToString(@"hh\:mm");
                this.schedule.WorkDays = userSetting.CalendarWorkDays.Select(c => c - '0').ToArray();
                this.timeZone = userSetting!.TimeZone;

                if (userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.SixtyMinutes)
                {
                    this.slotsCount = 2;
                    this.interval = 60;
                }
                else if (userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.ThirtyMinutes)
                {
                    this.slotsCount = 1;
                    this.interval = 60;
                }
                else if (userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.TwentyMinutes)
                {
                    this.slotsCount = 3;
                    this.interval = 60;
                }
                else if (userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.FifteenMinutes)
                {
                    this.slotsCount = 4;
                    this.interval = 60;
                }
                else if (userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.TenMinutes)
                {
                    this.slotsCount = 6;
                    this.interval = 60;
                }
                else if (userSetting.CalendarTimeScaleInterval == Data.Model.TimeScaleInterval.FiveMinutes)
                {
                    this.slotsCount = 6;
                    this.interval = 30;
                }
                this.schedule.FirstDayOfWeek = (int)userSetting!.FirstDayOfWeek;

                #region  Ρυθμίζει το τελευταίο View που είχε ο χρήστης.

                HybridCacheEntryOptions calendarLastViewCacheOptions = new HybridCacheEntryOptions
                {
                    LocalCacheExpiration = TimeSpan.FromMinutes(120)
                };
                View? view = await cache.GetOrCreateAsync<View>(Keywords.CalendarLastView,
                    async cancel =>
                    {
                        if (formFactor.GetDeviceIdiom() == "Web" || formFactor.GetDeviceIdiom() == "Desktop")
                        {
                            return await Task.FromResult(Enum.Parse<View>(userSetting.CalendarDefaultView.ToString()));
                        }
                        else if (formFactor.GetDeviceIdiom() == "Phone")
                        {
                            return await Task.FromResult(Enum.Parse<View>(userSetting.CalendarDefaultViewOnMobiles.ToString()));
                        }
                        else
                        {
                            return await Task.FromResult(View.Week);
                        }
                    },
                    calendarLastViewCacheOptions);

                if (view != null)  //Αν υπάρχει στο cache
                {
                    await this.schedule.ChangeCurrentViewAsync(view.Value);
                }
                else
                {
                    if (formFactor.GetDeviceIdiom() == "Web")
                    {
                        await this.schedule.ChangeCurrentViewAsync(Enum.Parse<View>(userSetting.CalendarDefaultView.ToString()));
                    }
                    else if (formFactor.GetDeviceIdiom() == "Phone")
                    {
                        await this.schedule.ChangeCurrentViewAsync(Enum.Parse<View>(userSetting.CalendarDefaultViewOnMobiles.ToString()));
                    }
                }
                #endregion


                //ΕΔΩ ΔΕΝ ΜΠΗΚΕ ΤΟ "Ρυθμίζει το displayUsersDropDown


                #region Διαβάζει τα EventCategories.
                HybridCacheEntryOptions eventCategoriesCacheOptions = new HybridCacheEntryOptions
                {
                    LocalCacheExpiration = TimeSpan.FromMinutes(60)
                };

                this.eventCategories = await cache.GetOrCreateAsync(
                    Keywords.EventCategories,
                    async cancel =>
                    {
                        EventCategoriesWebApiClient eventCategoriesWebApiClient = new EventCategoriesWebApiClient(httpClient, eventCategoriesWebApiClientLogger);
                        List<Data.Model.DBOs.Calendar.EventCategory>? tempEventCategories = await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);

                        ////Δημιουργεί το Dictionary με τα χρώματα.
                        //this.eventCategoriesColors.Clear();
                        //foreach (EventCategory eventCategory in tempEventCategories)
                        //{
                        //    this.eventCategoriesColors.Add(eventCategory.EventCategoryId.ToString(), eventCategory.ColorHex);
                        //}

                        return tempEventCategories;
                    },
                    eventCategoriesCacheOptions,
                    new[] { "Deletable" });
                #endregion

                #region  Δημιουργεί το array με τα χρώματα των EventCategories (για πιο γρήγορη αναζήτηση).
                this.eventCategoriesColors.Clear();  //Το καθαρίζει για να μην προστεθούν 2η φορά τα ίδια EventCategories, βγάζει σφάλμα.

                // Add a default entry for empty or null EventCategoryId
                this.eventCategoriesColors.Add("", "#FFFFFF");

                foreach (Data.Model.DBOs.Calendar.EventCategory eventCategory in this.eventCategories)
                {
                    this.eventCategoriesColors.Add(eventCategory.EventCategoryId.ToString(), eventCategory.BackColor);
                }
                #endregion

                #region  Διαβάζει τα Events.
                DateTime minDate = this.schedule.GetCurrentViewDates().Min().AddDays(-1);  //Αφαιρεί μια ημέρα για να καλύψει τη ανακρίβεια ώρας λόγω UTC.
                DateTime maxDate = this.schedule.GetCurrentViewDates().Max().AddDays(1);  //Προσθέτει μια ημέρα για να καλύψει τη ανακρίβεια ώρας λόγω UTC.
                Guid[]? visibleUsers = null;
                if (this.displayedUsersInScheduler.Count() > 0)
                {
                    visibleUsers = this.displayedUsersInScheduler!.Where(x => x.UserId != null).Select(x => x.UserId!.Value).ToArray();
                }

                Task<List<Data.Model.DBOs.Calendar.Event>> eventsTask = new WebApi.Client.EventsWebApiClient(httpClient, eventsWebApiClientLogger).GetEvents(AuthenticatedUserData.TenantId, "", visibleUsers, minDate, maxDate);
                List<Data.Model.DBOs.Calendar.Event> tempEvents = await eventsTask;

                //Ρυθμίζει το TimeZone στα Events.
                foreach (var e in tempEvents)
                {
                    e.UserTimeZoneId = userSetting!.TimeZone;
                }

                #region  Πριν δώσει τα tempEvents στο Scheduler ελέγχει αν έχει όλα τα EventCategories.
                // Foreach Event in tempEvents, checks if EventCategoryId does not exist in eventCategories.
                bool missingEventCategoryFound = false;
                foreach (var evt in tempEvents)
                {
                    if (!this.eventCategories.Any(ec => ec.EventCategoryId == evt.EventCategoryId))
                    {
                        missingEventCategoryFound = true;
                    }
                }
                if (missingEventCategoryFound)
                {
                    //Επαναλαμβάνει ξανά τη διαδικασία για τα EventCategories, την οποία έκανε παραπάνω.
                    EventCategoriesWebApiClient eventCategoriesWebApiClient = new EventCategoriesWebApiClient(httpClient, eventCategoriesWebApiClientLogger);
                    List<Data.Model.DBOs.Calendar.EventCategory>? tempEventCategories = await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);
                    tempEventCategories.Insert(0, new EventCategory() { EventCategoryId = Guid.Empty, Name = "", BackColor = "" });  //Προσθέτει το κενό EventCategory (αντιπροσωπεύει το null).

                    await cache.SetAsync(Keywords.EventCategories, tempEventCategories);
                    this.eventCategories = tempEventCategories;

                    #region  Δημιουργεί το array με τα χρώματα των EventCategories (για πιο γρήγορη αναζήτηση).
                    this.eventCategoriesColors.Clear();  //Το καθαρίζει για να μην προστεθούν 2η φορά τα ίδια EventCategories, βγάζει σφάλμα.

                    // Add a default entry for empty or null EventCategoryId
                    this.eventCategoriesColors.Add("", "#FFFFFF");

                    foreach (Data.Model.DBOs.Calendar.EventCategory eventCategory in this.eventCategories)
                    {
                        this.eventCategoriesColors.Add(eventCategory.EventCategoryId.ToString(), eventCategory.BackColor);
                    }
                    #endregion
                }
                #endregion

                this.events.Clear();
                this.events = tempEvents;
                #endregion

                await this.schedule.RefreshEventsAsync(true);
            }
            catch (Exception ex)
            {
                int i = 0;
            }
        }

        private async Task NewEvent()
        {
            try
            {
                this.schedule.PreventRender();

                DateTime startTime = DateTime.Now.AddHours(1);
                startTime = new DateTime(startTime.Year, startTime.Month, startTime.Day, startTime.Hour, 0, 0);
                DateTime endTime = startTime.AddHours(1);

                Data.Model.DBOs.Calendar.Event eventObj = Data.Model.DBOs.Calendar.Event.CreateEvent(this.userSetting!.TimeZone, startTime, startTime.AddHours(1), AuthenticatedUserData.TenantId, null);  //TODO: Κανονικά εδώ πρέπει να βάζει διάρκεια με βάση τα settings ή το timescale του ημερολογίου.

                await this.ShowEvent(eventObj, WebApi.Shared.Request.RecurrentEventHandlingType.Current);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        private async Task<Data.Model.DBOs.Calendar.Event?> ReloadEvent(Data.Model.DBOs.Calendar.Event eventObj)
        {
            UserSetting? userSetting = await cache.GetOrCreateAsync(
                Keywords.UserSetting,
                async cancel =>
                {
                    UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                    return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                });

            //Διαβάζει τα δεδομένα.
            EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
            Data.Model.DBOs.Calendar.Event? reloadedEventObj = await eventsWebApiClient.GetEvent(eventObj.EventId);

            if (reloadedEventObj != null)
            {
                reloadedEventObj.UserTimeZoneId = userSetting!.TimeZone;
            }

            return reloadedEventObj;
        }

        private async Task EditEvent(Event evnt)
        {
            try
            {
                WebApi.Shared.Request.RecurrentEventHandlingType recurrentEventHandlingType = WebApi.Shared.Request.RecurrentEventHandlingType.Current;

                //If Event is recurrent.
                if (evnt.CustomRecurrence == true)
                {
                    var parameters = new DialogParameters()
                    {
                        Title = "",
                        PreventDismissOnOverlayClick = true,
                        PrimaryAction = "",
                        SecondaryAction = "",
                        Modal = true,
                        ShowDismiss = false
                    };
                    var dialog = await dialogService.ShowDialogAsync<RecurrentEventHandlingDialog>(Components.RecurrentEventHandlingType.Update, parameters);
                    var dialogResult = await dialog.Result;

                    if (dialogResult.Cancelled)  //If user wants to cancel the update.
                    {
                        return;
                    }
                    else
                    {
                        recurrentEventHandlingType = (WebApi.Shared.Request.RecurrentEventHandlingType)dialogResult.Data!;
                    }
                }
                Event evnt2 = evnt.Clone();  //Δημιουργούμε ένα ακριβές αντίγραφο του Event γιατί το αρχικό Event μπορεί να έχει αλλάξει από το SignalR.
                await ShowEvent(evnt2, recurrentEventHandlingType);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        /// <summary>
        /// Deletes an Event asking for confirmation.
        /// </summary>
        /// <param name="evnt"></param>
        /// <returns>A value indicating if deletion was performed.</returns>
        private async Task<bool> DeleteEvent(Event evnt)
        {
            WebApi.Shared.Request.RecurrentEventHandlingType recurrentEventHandlingType = WebApi.Shared.Request.RecurrentEventHandlingType.Current;

            //If Event is recurrent.
            if (evnt.CustomRecurrence == true)
            {
                var parameters = new DialogParameters()
                {
                    Title = "",
                    PreventDismissOnOverlayClick = true,
                    PrimaryAction = "",
                    SecondaryAction = "",
                    Modal = true,
                    ShowDismiss = false
                };
                var dialog = await dialogService.ShowDialogAsync<RecurrentEventHandlingDialog>(Components.RecurrentEventHandlingType.Delete, parameters);
                var dialogResult = await dialog.Result;

                if (dialogResult.Cancelled)  //If user wants to cancel the update.
                {
                    return false;
                }
                else
                {
                    recurrentEventHandlingType = (WebApi.Shared.Request.RecurrentEventHandlingType)dialogResult.Data!;
                }
            }
            else  //If it is a normal (single) Event.
            {
                var dialog = await dialogService.ShowConfirmationAsync(Main.GlobalResource.DeleteDataConfirmation, Main.GlobalResource.Yes, Main.GlobalResource.No, GlobalResource.Delete);
                var dialogResult = await dialog.Result;

                if (dialogResult.Cancelled)  //If user wants to cancel the update.
                {
                    return false;
                }
            }

            EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
            await eventsWebApiClient.DeleteEvent(evnt.EventId, recurrentEventHandlingType);

            return true;
        }


        private async Task ShowEvent(Data.Model.DBOs.Calendar.Event evnt, WebApi.Shared.Request.RecurrentEventHandlingType recurrentEventHandingType)
        {
            try
            {
                string dialogWidth = "1000px", dialogHeight = "90%";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                //εμφανίζει το dialog
                DialogParameters<Data.Model.DBOs.Calendar.Event> parameters = new()
                {
                    ShowTitle = true,
                    OnDialogResult = dialogService.CreateDialogCallback(this, OnEventDialogResult),
                    Title = UI.Main.Pages.Resources.CalendarResource.Event,
                    PrimaryAction = "",  //GlobalResource.Save,
                    SecondaryAction = "", //=GlobalResource.Cancel,
                    Width = dialogWidth,
                    Height = dialogHeight,
                    TrapFocus = false,
                    Modal = true,
                    //PreventScroll = true,
                    PreventDismissOnOverlayClick = true,
                    ShowDismiss = false,
                    Alignment = HorizontalAlignment.Center
                };
                EventDialogInput eventDialogInput = new EventDialogInput() { Event = evnt, RecurrentEventHandlingType = recurrentEventHandingType };
                dialog = await dialogService.ShowDialogAsync<UI.Main.Components.EventDialog>(eventDialogInput, parameters);
                DialogResult? result = await dialog.Result;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        private async Task OnEventDialogResult(DialogResult result)
        {
            try
            {
                if (result.Cancelled == false)
                {
                    await InvokeAsync(async () => { await LoadEvents(); });
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public async Task ScheduleOnEventClick(EventClickArgs<Event> args)
        {
            try
            {
                if (args.Event != null && args.MouseEventArgs.Button == 2)
                {
                    //this.scheduleMenu.OpenAsync((int)args.MouseEventArgs.PageX, (int)args.MouseEventArgs.PageY);
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ScheduleOnEventDoubleClick(EventClickArgs<Data.Model.DBOs.Calendar.Event> args)
        {
            try
            {
                args.Cancel = true;  //Ακυρώνουμε το κανονικό event.
                //Data.Model.DBOs.Calendar.Event? reloadedEventObj = await this.ReloadEvent(args.Event);

                //Αν πράγματι υπάρχει το event στη βάση δεδομένων.
                if (args.Event != null)
                {
                    await ShowEvent(args.Event, WebApi.Shared.Request.RecurrentEventHandlingType.Current);
                }
                else
                {
                    var dialog = await dialogService.ShowInfoAsync(Pages.Resources.CalendarResource.EventNotExists);  //TODO: ίσως να μπει ερώτηση στο χρήστη αν θέλει να γίνει αυτόματα το sync.
                    DialogResult? result = await dialog.Result;
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public async Task ScheduleOnDragged(DragEventArgs<Data.Model.DBOs.Calendar.Event> args)
        {
            try
            {
                if (args.Data != null)
                {
                    this.finalGroupIndexWhileDragging = args.GroupIndex;  //Set the final GroupIndex of the resource (Users) since the drag-drop is stopped.

                    //If the Event changed resource (User)
                    if (this.initialGroupIndexWhileDragging != this.finalGroupIndexWhileDragging)
                    {
                        //Finds the intial User of Event before dragging.
                        UserLI initialUser = (UserLI)this.schedule.GetResourceCollections()[0].DataSource.ToList()[this.initialGroupIndexWhileDragging.Value];

                        //Finds the "new" User where Event was dragged to.
                        UserLI newUser = (UserLI)this.schedule.GetResourceCollections()[0].DataSource.ToList()[this.finalGroupIndexWhileDragging.Value];

                        //If Event is already associated with the "new" User.
                        if (args.Data.EventUsers.Any(x => x.UserId == newUser.UserId))
                        {
                            //Do nothing, because the Event belongs to 2 (or more) Users and it was dragged to the same User (from 1st to 2nd).
                            args.Cancel = true;

                            this.initialGroupIndexWhileDragging = null;
                            this.finalGroupIndexWhileDragging = null;
                            return;
                        }
                        else  //If Event is indeed dragged to a "new" User.
                        {
                            //Removes the initial User
                            if (args.Data.EventUsers.Where(x => x.UserId == initialUser.UserId).FirstOrDefault() != null)
                            {
                                args.Data.EventUsers.Where(x => x.UserId == initialUser.UserId).FirstOrDefault()!.ObjectState = ObjectState.Deleted;
                            }
                            //Adds the new User
                            args.Data.AddNewEventUser(newUser.UserId);
                        }
                    }

                    //Αν το Event είναι AllDay
                    if (args.Data.AllDay)
                    {
                        //Αφαιρεί μια ημέρα γιατί το Syncfusion Scheduler ορίζει το EndTime την επόμενη ημέρα το πρωί στις 12:00
                        if (args.Data.EndTimeLocal!.Value.Date > args.Data.StartTimeLocal!.Value.Date)
                        {
                            args.Data.EndTimeLocal = args.Data.EndTimeLocal!.Value.AddDays(-1);
                        }
                    }

                    //NOTE: we don't save the Event here, because it will be handled in ScheduleOnActionCompleted. The same for reloading events.
                }

                this.dragging = false;
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {
                this.dragging = false;
            }
        }

        private async Task ScheduleOnActionBegin(ActionEventArgs<Data.Model.DBOs.Calendar.Event> args)
        {
            try
            {
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ScheduleOnActionCompleted(ActionEventArgs<Data.Model.DBOs.Calendar.Event> args)
        {
            try
            {
                if (this.pageLoaded == false) return;

                if (args.ActionType == ActionType.DateNavigate)
                {
                    await InvokeAsync(async () => { await LoadEvents(); });
                }
                else if (args.ActionType == ActionType.ViewNavigate)
                {
                    //await cache.SetAsync(Keywords.CalendarLastView, this.schedule.CurrentView);

                    HybridCacheEntryOptions calendarLastViewCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(120) };
                    await cache.SetAsync<View>(Keywords.CalendarLastView, schedule.CurrentView, calendarLastViewCacheOptions);

                    await InvokeAsync(async () => { await LoadEvents(); });
                }
                else if (args.ActionType == ActionType.EventCreate)
                {
                    EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                    await eventsWebApiClient.CreateEvent(args.AddedRecords[0], null);

                    await InvokeAsync(async () => { await LoadEvents(); });
                }
                else if (args.ActionType == ActionType.EventChange)
                {
                    if (args.ChangedRecords.Count > 0)
                    {
                        args.ChangedRecords[0].ObjectState = Data.Model.ObjectState.Modified;

                        EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                        await eventsWebApiClient.UpdateEvent(args.ChangedRecords[0], WebApi.Shared.Request.RecurrentEventHandlingType.Current, null);
                    }
                    await InvokeAsync(async () => { await LoadEvents(); });
                }
                else if (args.ActionType == ActionType.EventRemove)
                {
                    EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                    await eventsWebApiClient.DeleteEvent(args.DeletedRecords[0].EventId, null);
                    await InvokeAsync(async () => { await LoadEvents(); });
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ScheduleOnPopupOpen(PopupOpenEventArgs<Data.Model.DBOs.Calendar.Event> args)
        {
            try
            {
                if (args.Type == PopupType.QuickInfo)  //Όταν ανοίγει το QuickInfo (γία νέο ή υπάρχον Event)
                {
                    //Αν το Event δεν έχει TenantId είναι για νέο Event.
                    if (args.Data.TenantId == null)
                    {
                        args.Data.ObjectState = Data.Model.ObjectState.Added;
                        args.Data.TenantId = AuthenticatedUserData.TenantId;
                    }
                }
                else if (args.Type == PopupType.Editor)
                {
                    args.Cancel = true;

                    //Αν το Event δεν έχει TenantId είναι για νέο Event.
                    if (args.Data.TenantId == null)
                    {
                        args.Data.ObjectState = Data.Model.ObjectState.Added;
                        args.Data.TenantId = AuthenticatedUserData.TenantId;

                        await ShowEvent(args.Data, WebApi.Shared.Request.RecurrentEventHandlingType.Current);
                    }
                    else  //If we edit the Event.
                    {
                        //Data.Model.DBOs.Calendar.Event? reloadedEventObj = await this.ReloadEvent(args.Data);

                        //Αν πράγματι υπάρχει το event στη βάση δεδομένων.
                        if (args.Data != null)
                        {
                            await EditEvent(args.Data);
                        }
                        else
                        {
                            var dialog = await dialogService.ShowInfoAsync(Pages.Resources.CalendarResource.EventNotExists);  //TODO: ίσως να μπει ερώτηση στο χρήστη αν θέλει να γίνει αυτόματα το sync.
                            DialogResult? result = await dialog.Result;
                        }
                    }
                }
                else if (args.Type == PopupType.DeleteAlert)
                {
                    args.Cancel = true;

                    //Data.Model.DBOs.Calendar.Event? reloadedEventObj = await this.ReloadEvent(args.Data);

                    //Αν πράγματι υπάρχει το Event στη βάση δεδομένων.
                    if (args.Data != null)
                    {
                        bool deletePerformed = await this.DeleteEvent(args.Data);
                        if (deletePerformed == true)
                        {
                            await InvokeAsync(async () => { await LoadEvents(); });
                        }
                    }
                    else
                    {
                        var dialog = await dialogService.ShowInfoAsync(Pages.Resources.CalendarResource.EventNotExists);  //TODO: ίσως να μπει ερώτηση στο χρήστη αν θέλει να γίνει αυτόματα το sync.
                        DialogResult? result = await dialog.Result;
                    }
                }
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ScheduleOnPopupClose(PopupCloseEventArgs<Data.Model.DBOs.Calendar.Event> args)
        {
            try
            {
                if (args.Type == PopupType.Editor)
                {
                    args.Cancel = true;   //To prevent start and end time validation alert
                                          //await ShowEvent(args.Data.EventId);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public void ScheduleOnEventRendered(EventRenderedArgs<Data.Model.DBOs.Calendar.Event> args)
        {
            try
            {
                // First check if args or args.Data is null
                if (args == null || args.Data == null)
                {
                    return; // Exit early if we don't have valid data
                }

                #region Set the background based on EventCategory.
                try
                {
                    Dictionary<string, object> attributes = new Dictionary<string, object>();
                    string eventCategoryIdKey = args.Data.EventCategoryId?.ToString() ?? "";
                    string backgroundColor = "#FFFFFF"; // Default background color

                    // Check if eventCategoriesColors is initialized
                    if (this.eventCategoriesColors != null && this.eventCategoriesColors.Count > 0)
                    {
                        // Use TryGetValue for better performance and safety
                        if (this.eventCategoriesColors.TryGetValue(eventCategoryIdKey, out string? color))
                        {
                            backgroundColor = color;
                        }
                    }

                    // Always set attributes regardless of whether a color was found
                    attributes.Add("style", "background: " + backgroundColor);
                    args.Attributes = attributes;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error setting event background color");
                    // Set a default style if there's an error
                    try
                    {
                        Dictionary<string, object> defaultAttributes = new Dictionary<string, object>();
                        defaultAttributes.Add("style", "background: #FFFFFF");
                        args.Attributes = defaultAttributes;
                    }
                    catch
                    {
                        // Last resort - if we can't even set default attributes, just log and continue
                        logger.LogError("Failed to set default attributes");
                    }
                }
                #endregion

                //Set the repeat icon based on CustomRecurrence.
                try
                {
                    if (args.Data.CustomRecurrence)
                    {
                        // Initialize CssClasses if it's null
                        if (args.CssClasses == null)
                        {
                            args.CssClasses = new List<string>();
                        }

                        // Add the custom-recurrence class if it's not already there
                        if (!args.CssClasses.Contains("custom-recurrence"))
                        {
                            args.CssClasses.Add("custom-recurrence");
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error setting custom recurrence CSS class");
                }

                #region  Set the css for cut Event 
                if (args.Data.EventId == this.cutEventId)
                {
                    // Initialize CssClasses if it's null
                    if (args.CssClasses == null)
                    {
                        args.CssClasses = new List<string>();
                    }
                    args.CssClasses.Add("cut-event");
                }
                #endregion
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public void ScheduleOnDragStart(DragEventArgs<Event> args)
        {
            try
            {
                this.dragging = true;

                args.ExcludeSelectors = "e-header-cells"; //We prevent dragging from header cells  //",e-all-day-cells";  //We prevent dragging from all-day cells
                this.initialGroupIndexWhileDragging = args.GroupIndex;  //Set the initial GroupIndex of the resource (Users) when the drag started.
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public async Task ScheduleOnResized(ResizeEventArgs<Event> args)
        {
            try
            {
                args.Cancel = true;   //To prevent resize action
                if (args.Data != null)
                {
                    args.Data.ObjectState = Data.Model.ObjectState.Modified;

                    //Αν το Event είναι AllDay αφαιρεί μια ημέρα γιατί το Syncfusion Scheduler ορίζει το EndTime την επόμενη ημέρα το πρωί στις 12:00
                    if (args.Data.AllDay)
                    {
                        if (args.Data.EndTimeLocal!.Value.Date > args.Data.StartTimeLocal!.Value.Date)
                        {
                            args.Data.EndTimeLocal = args.Data.EndTimeLocal!.Value.AddDays(-1);
                        }
                    }

                    EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                    await eventsWebApiClient.UpdateEvent(args.Data, WebApi.Shared.Request.RecurrentEventHandlingType.Current, null);
                }
                await InvokeAsync(async () => { await LoadEvents(); });
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public async Task ScheduleOnCellDoubleClick(CellClickEventArgs args)
        {
            try
            {
                //Finds the intial User of Event before dragging.
                UserLI user = (UserLI)this.schedule.GetResourceCollections()[0].DataSource.ToList()[args.GroupIndex];

                var newEvent = Data.Model.DBOs.Calendar.Event.CreateEvent(
                    this.userSetting!.TimeZone,
                    args.StartTime,
                    args.EndTime,
                    AuthenticatedUserData.TenantId,
                    user.UserId
                );

                await ShowEvent(newEvent, WebApi.Shared.Request.RecurrentEventHandlingType.Current);
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private void DisplayUsersDropDownOnVisibleUsersSearch(OptionsSearchEventArgs<UserLI> e)
        {
            try
            {
                if (this.users != null)
                {
                    e.Items = this.users.Where(x => x.FullName.Contains(e.Text, StringComparison.OrdinalIgnoreCase) && x.AvailableInEventUsers == true).OrderBy(x => x.FullName).ToList();
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        //private async void DisplayUsersDropDownOnSelectedOptionsChanged(IEnumerable<UserLI>? selectedUsers)
        //{
        //    try
        //    {


        //        bool needReloadEvents = false;

        //        if (this.selectedDisplayUsers != null && this.displayUsersDropDown != null && this.displayUsersDropDown.SelectedOptions != null)
        //        {
        //            //If variable scheduler displays any UserLI which is not selected int displayUsersDropDown.
        //            foreach (UserLI user in selectedUsers)
        //            {
        //                //if selectedDisplayUsers do not contain the user.
        //                if (this.selectedDisplayUsers.Where(x => x.UserId == user.UserId).Any() == false)
        //                {
        //                    needReloadEvents = true;
        //                }
        //            }

        //            //If displayUsersDropDown has any UserLI which is not displayed in the scheduler.
        //            foreach (UserLI user in this.selectedDisplayUsers)
        //            {
        //                //if selectedDisplayUsers do not contain the user.
        //                if (selectedUsers.Where(x => x.UserId == user.UserId).Any() == false)
        //                {
        //                    needReloadEvents = true;
        //                }
        //            }


        //            if (needReloadEvents)
        //            {
        //                this.events = new List<Event>();
        //                displayedUsersInScheduler.Clear();
        //                foreach (var u in selectedUsers.ToList())
        //                    displayedUsersInScheduler.Add(u);
        //                //await cache.SetAsync<List<Data.Model.DTOs.UserLI>?>(Keywords.LastSelectedCalendarDisplayUsers, this.selectedDisplayUsers.ToList());

        //                await cache.RemoveAsync(Keywords.LastSelectedCalendarDisplayUsers);  //Καθαρίζει το cache γιατί το χρησιμοποιήσαμε.
        //                await cache.SetAsync<List<Data.Model.DTOs.UserLI>?>(Keywords.LastSelectedCalendarDisplayUsers, displayedUsersInScheduler.ToList());  //Αποθηκεύει το cache με τους επιλεγμένους display Users που εμφανίζονται στο ημερολόγιο.

        //                await InvokeAsync(async () => { await LoadEvents(); });
        //                await this.schedule.RefreshEventsAsync(true);
        //            }
        //            this.StateHasChanged();
        //        }



        //        return;
        //        ////Αν ο χρήστης επέλεξε τουλάχιστον έναν User.
        //        //if (selectedUsers != null && selectedUsers.Count() > 0)
        //        //{
        //        //    //this.selectedDisplayUsers = selectedUsers.AsEnumerable();
        //        //    this.selectedDisplayUsers2 = new ObservableCollection<UserLI>(selectedUsers);
        //        //    await cache.SetAsync<List<Data.Model.DTOs.UserLI>>(Keywords.LastSelectedCalendarDisplayUsers, selectedUsers.ToList());
        //        //}
        //        //else  //Αν δεν επιλέχθηκε κανένας User.
        //        //{
        //        //    UserLI? userLI = this.users.Where(x => x.AvailableInEventUsers == true).FirstOrDefault();  //Εδώ θα πρέπει πάντα να υπάρχει τουλάχιστον ένας User που να επιτρέπεται να εμφανίζεται στο ημερολόγιο.
        //        //    List<UserLI> usersToSelect = new List<UserLI>() { userLI! };
        //        //    //this.selectedDisplayUsers = usersToSelect.AsEnumerable();
        //        //    this.selectedDisplayUsers2 = new ObservableCollection<UserLI>(usersToSelect);
        //        //}

        //        //await this.LoadEvents2();

        //        //Αν ο χρήστης επέλεξε τουλάχιστον έναν User.
        //        if (selectedUsers != null && selectedUsers.Count() > 0)
        //        {
        //            //this.selectedDisplayUsers = selectedUsers.AsEnumerable();
        //            //this.selectedDisplayUsers2 = new ObservableCollection<UserLI>(selectedUsers);
        //            foreach (UserLI user in selectedUsers)
        //            {
        //                //if (this.selectedDisplayUsers2.Where(x => x.UserId == user.UserId).Any() == false)
        //                {
        //                    user.Expanded = !user.Expanded;
        //                    //this.selectedDisplayUsers2.Add(new UserLI()
        //                    //{
        //                    //    //set all the properties based on user
        //                    //    FullName = user.FullName,
        //                    //    UserId = user.UserId,
        //                    //    AvailableInEventUsers = user.AvailableInEventUsers,
        //                    //});
        //                }
        //            }

        //            // If there is any UserLI in selectedDisplayUsers2 which does not exist in selectedUsers then it removes it.
        //            foreach (UserLI user in this.displayedUsersInScheduler)
        //            {
        //                if (selectedUsers.Where(x => x.UserId == user.UserId).Any() == false)
        //                {
        //                    //this.selectedDisplayUsers2.Remove(user);
        //                    //user.Expanded = true;
        //                }
        //            }



        //            //foreach (UserLI user in this.selectedDisplayUsers2)
        //            //{
        //            //    if (selectedUsers.Where(x => x.UserId != user.UserId).Any() == true)
        //            //    {
        //            //        this.selectedDisplayUsers2.Remove(user);
        //            //    }
        //            //}
        //            await cache.SetAsync<List<Data.Model.DTOs.UserLI>>(Keywords.LastSelectedCalendarDisplayUsers, selectedUsers.ToList());

        //            //this.selectedDisplayUsers2 = new ObservableCollection<UserLI>(selectedUsers).ToList();

        //        }
        //        else  //Αν δεν επιλέχθηκε κανένας User.
        //        {
        //            UserLI? userLI = this.users.Where(x => x.AvailableInEventUsers == true).FirstOrDefault();  //Εδώ θα πρέπει πάντα να υπάρχει τουλάχιστον ένας User που να επιτρέπεται να εμφανίζεται στο ημερολόγιο.
        //            //List<UserLI> usersToSelect = new List<UserLI>() { userLI! };
        //            //this.selectedDisplayUsers = usersToSelect.AsEnumerable();
        //            //this.selectedDisplayUsers2.Clear();
        //            //this.selectedDisplayUsers2.Add(userLI!);
        //            userLI.Expanded = true;
        //        }

        //        await InvokeAsync(async () => { await LoadEvents(); });
        //    }
        //    catch (ApplicationException ex)
        //    {
        //        logger.LogError(ex, ex.Message);
        //        new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
        //    }
        //    catch (Exception ex)
        //    {
        //        logger.LogError(ex, ex.Message);
        //        new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
        //    }
        //}

        private async void DisplayUsersDropDownOnValueChanged(string value)
        {
            try
            {
                bool needReloadEvents = false;

                if (this.selectedDisplayUsers != null && this.displayUsersDropDown != null && this.displayUsersDropDown.SelectedOptions != null)
                {
                    if (selectedDisplayUsers.Count() > 0)
                    {
                        //If variable scheduler displays any UserLI which is not selected int displayUsersDropDown.
                        foreach (UserLI user in this.displayedUsersInScheduler)
                        {
                            //if selectedDisplayUsers do not contain the user.
                            if (this.selectedDisplayUsers.Where(x => x.UserId == user.UserId).Any() == false)
                            {
                                needReloadEvents = true;
                            }
                        }

                        //If displayUsersDropDown has any UserLI which is not displayed in the scheduler.
                        foreach (UserLI user in this.selectedDisplayUsers)
                        {
                            //if selectedDisplayUsers do not contain the user.
                            if (this.displayedUsersInScheduler.Where(x => x.UserId == user.UserId).Any() == false)
                            {
                                needReloadEvents = true;
                            }
                        }

                        if (needReloadEvents)
                        {
                            this.events = new List<Event>();
                            displayedUsersInScheduler.Clear();
                            foreach (var u in this.selectedDisplayUsers)
                                displayedUsersInScheduler.Add(u);

                            await cache.RemoveAsync(Keywords.LastSelectedCalendarDisplayUsers);  //Καθαρίζει το cache γιατί το χρησιμοποιήσαμε.
                            await cache.SetAsync<List<Data.Model.DTOs.UserLI>?>(Keywords.LastSelectedCalendarDisplayUsers, displayedUsersInScheduler.ToList());  //Αποθηκεύει το cache με τους επιλεγμένους display Users που εμφανίζονται στο ημερολόγιο.

                            await InvokeAsync(async () => { await LoadEvents(); });
                            await this.schedule.RefreshEventsAsync(true);
                        }
                        this.StateHasChanged();
                    }
                    else  //Αν δεν επιλέχθηκε κανένας User.
                    {
                        //Βρίσκει το τελευταίο επιλεγμένο UserLI (που θα το βρει στη cache)
                        HybridCacheEntryOptions lastSelectedDisplayUsersCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };//Δεν θέλουμε να καθαρίσει όσο η εφαρμογή τρέχει.
                        List<Data.Model.DTOs.UserLI>? lastSelectedDisplayUsers = await cache.GetOrCreateAsync<List<Data.Model.DTOs.UserLI>?>(
                            Keywords.LastSelectedCalendarDisplayUsers,
                            async cancel => await Task.FromResult<List<Data.Model.DTOs.UserLI>?>(null),
                            lastSelectedDisplayUsersCacheOptions);

                        if (lastSelectedDisplayUsers != null && lastSelectedDisplayUsers.Count > 0)
                        {
                            displayedUsersInScheduler.Clear();
                            foreach (var u in lastSelectedDisplayUsers)
                            {
                                //ΠΡΟΣΟΧΗ: τα UserLI που βάζουμε στο displayedUsersInScheduler και στο selectedDisplayUsers πρέπει να προέρχονται πάντα από το displayUsersDS.
                                this.displayedUsersInScheduler.Add(this.displayUsersDS.Where(x => x.UserId == u.UserId).FirstOrDefault()!);
                            }
                            this.selectedDisplayUsers = this.displayedUsersInScheduler;
                        }
                        else
                        {
                            UserLI? userLI = this.displayUsersDS.FirstOrDefault();  //Εδώ θα πρέπει πάντα να υπάρχει τουλάχιστον ένας User που να επιτρέπεται να εμφανίζεται στο ημερολόγιο.
                            displayedUsersInScheduler.Clear();
                            displayedUsersInScheduler.Add(userLI!);  //ΠΡΟΣΟΧΗ: τα UserLI που βάζουμε στο displayedUsersInScheduler και στο selectedDisplayUsers πρέπει να προέρχονται πάντα από το displayUsersDS.
                            this.selectedDisplayUsers = this.displayedUsersInScheduler;
                        }
                        this.StateHasChangedOptimized();
                    }
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        private async Task Sync()
        {
            try
            {
                await cache.RemoveByTagAsync("Deletable");
                await InvokeAsync(async () => { await LoadEvents(); });

                this.StateHasChangedOptimized();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        public async Task ScheduleMenuOnOpen(BeforeOpenCloseMenuEventArgs<MenuItem> args)
        {
            try
            {
                menuClickedCell = menuClickedEvent = menuClickedRecurrentEvent = false;
                contextMenuEvent = null;
                contextMenuCell = null;
                contextMenuUser = null;

                if (args.ParentItem == null && args.Left != null && args.Top != null)
                {
                    ElementInfo<Event>? elementInfo = null;
                    try
                    {
                        //List<Event> selected = await schedule!.GetSelectedEventsAsync();

                        await InvokeAsync(async () => { elementInfo = await schedule.GetElementInfoAsync((int)args.Left, (int)args.Top); });
                        //elementInfo = await schedule.GetElementInfoAsync((int)args.Left, (int)args.Top);
                        if (elementInfo != null)
                        {
                            if (elementInfo.ResourceData != null)
                            {
                                foreach (object resourceData in elementInfo.ResourceData)
                                {
                                    if (resourceData is UserLI)
                                    {
                                        this.contextMenuUser = (UserLI)resourceData;
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, ex.Message);
                        args.Cancel = true;
                        return;
                    }

                    if (elementInfo != null)
                    {
                        if (elementInfo.ElementType == ElementType.Event)
                        {
                            contextMenuEvent = elementInfo.EventData;
                            // EventId is a Guid and cannot be null, but we'll check if it's empty
                            if (contextMenuEvent.EventId == Guid.Empty)
                            {
                                args.Cancel = true;
                                return;
                            }

                            if (contextMenuEvent.CustomRecurrence == true)
                            {
                                menuClickedCell = menuClickedRecurrentEvent = true;
                                menuClickedEvent = true;
                            }
                            else
                            {
                                menuClickedCell = menuClickedEvent = true;
                                menuClickedRecurrentEvent = false;
                            }
                        }
                        else if (elementInfo.ElementType == ElementType.WorkCells || elementInfo.ElementType == ElementType.MonthCells)
                        {
                            if (contextMenuCell == null)  //ATTENTION: We set this check because the event is executed 2 times and the 2nd time StartTime and EndTime are wrong (the same).
                            {
                                contextMenuCell = new CellClickEventArgs
                                {
                                    StartTime = elementInfo.StartTime,
                                    EndTime = elementInfo.EndTime,
                                    IsAllDay = elementInfo.IsAllDay
                                };
                            }
                            menuClickedCell = true;
                            menuClickedEvent = menuClickedRecurrentEvent = false;
                        }
                        else
                        {
                            args.Cancel = true;
                        }
                    }
                    else
                    {
                        args.Cancel = true;
                    }
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public async Task ScheduleMenuOnClose(BeforeOpenCloseMenuEventArgs<MenuItem> args)
        {
            try
            {
                // Do not clear contextMenuCell here, as it's needed for paste operation
                contextMenuEvent = null;
                // Keep contextMenuCell and contextMenuUser for paste operations
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ScheduleMenuOnItemSelected(MenuEventArgs<MenuItem> args)
        {
            try
            {
                this.calendarContextMenu.Close();  //Κλείνουμε το menu γιατί δεν κλείνει από μόνο του.

                switch (args.Item.Id)
                {
                    case "NewEvent":
                        if (contextMenuCell != null)
                        {
                            var newEvent = Data.Model.DBOs.Calendar.Event.CreateEvent(
                                this.userSetting!.TimeZone,
                                contextMenuCell.StartTime,
                                contextMenuCell.EndTime,
                                AuthenticatedUserData.TenantId,
                                this.contextMenuUser?.UserId
                            );

                            await ShowEvent(newEvent, WebApi.Shared.Request.RecurrentEventHandlingType.Current);
                            //await this.LoadEvents();
                        }
                        break;
                    case "Delete":
                        if (contextMenuEvent != null)
                        {
                            //var reloadedEvent = await ReloadEvent(contextMenuEvent);
                            if (contextMenuEvent != null)
                            {
                                bool deletionResult = await this.DeleteEvent(contextMenuEvent);
                                if (deletionResult == true)
                                {
                                    await InvokeAsync(async () => { await LoadEvents(); });
                                }
                            }
                        }
                        break;
                    case "EditEvent":
                        if (contextMenuEvent != null)
                        {
                            //var reloadedEvent = await ReloadEvent(contextMenuEvent);
                            if (contextMenuEvent != null)
                            {
                                await this.EditEvent(contextMenuEvent);
                                await InvokeAsync(async () => { await LoadEvents(); });
                            }
                        }
                        break;
                    case "DuplicateEvent":
                        if (contextMenuEvent != null)
                        {
                            Event duplicatedEvent = contextMenuEvent.CopyAsNew();

                            await ShowEvent(duplicatedEvent, WebApi.Shared.Request.RecurrentEventHandlingType.Current);
                        }
                        break;
                    case "CopyEvent":
                        if (contextMenuEvent != null)
                        {
                            // Store in cache for persistence
                            await StoreCopiedEventInCache(this.contextMenuEvent);
                        }
                        break;
                    case "CutEvent":
                        if (contextMenuEvent != null)
                        {
                            // Store in cache for persistence
                            await StoreCutEventInCache(this.contextMenuEvent);
                            this.cutEventId = contextMenuEvent.EventId;  //Θα χρησιμοποιηθεί για να μορφόποιήσει το css του Event.
                            await this.schedule.RefreshAsync();
                        }
                        break;
                    case "PasteEvent":
                        Event? copiedEvent = await RetrieveCopiedEventFromCache();
                        Event? cutEvent = await RetrieveCutEventFromCache();

                        //Αν υπάρχει copiedEvent.
                        if (copiedEvent != null)
                        {
                            if (contextMenuCell == null)
                            {
                                // Show a message that the paste location is invalid
                                this.toastService.ShowInfo(Resources.CalendarResource.EventPasteNotAllowedHere);
                                break;
                            }

                            // Create a clone of the copied event instead of modifying the original
                            Event eventToPaste = copiedEvent.Clone();

                            TimeSpan duration = copiedEvent.EndTimeLocal!.Value - copiedEvent.StartTimeLocal!.Value;

                            // Handle different views
                            if (this.schedule.CurrentView == View.Month || this.schedule.CurrentView == View.Agenda)
                            {
                                // In Month or Agenda view, maintain the original time but change the date
                                DateTime newStartDate = new DateTime(
                                    contextMenuCell.StartTime.Year,
                                    contextMenuCell.StartTime.Month,
                                    contextMenuCell.StartTime.Day,
                                    copiedEvent.StartTimeLocal!.Value.Hour,
                                    copiedEvent.StartTimeLocal!.Value.Minute,
                                    copiedEvent.StartTimeLocal!.Value.Second);

                                eventToPaste.StartTimeLocal = newStartDate;
                                eventToPaste.EndTimeLocal = newStartDate.Add(duration);
                            }
                            else  //Αν η νέα ημερομηνία έχει ώρα γιατί είναι π.χ. από Day view.
                            {
                                eventToPaste.StartTimeLocal = new DateTime(contextMenuCell.StartTime!.Year, contextMenuCell.StartTime!.Month, contextMenuCell.StartTime.Day, this.contextMenuCell.StartTime.Hour, this.contextMenuCell.StartTime.Minute, this.contextMenuCell.StartTime.Second);
                                eventToPaste.EndTimeLocal = eventToPaste.StartTimeLocal.Value.Add(duration);
                            }

                            // If the paste was in a different user's calendar, update the event user
                            if (this.contextMenuUser != null)
                            {
                                // Clear existing users and add the new one
                                eventToPaste.EventUsers.Clear();
                                eventToPaste.AddNewEventUser(this.contextMenuUser.UserId);
                            }

                            // Saves the pasted Event                           
                            EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                            await eventsWebApiClient.UpdateEvent(eventToPaste, WebApi.Shared.Request.RecurrentEventHandlingType.Current, null);
                            await InvokeAsync(async () => { await LoadEvents(); });
                        }
                        else if (cutEvent != null)  //Αν υπάρχει cutEvent.
                        {
                            if (contextMenuCell == null)
                            {
                                // Show a message that the paste location is invalid
                                this.toastService.ShowInfo(Resources.CalendarResource.EventPasteNotAllowedHere);
                                break;
                            }

                            // Create a clone of the copied event instead of modifying the original
                            //Event eventToPaste = cutEvent.Clone();

                            TimeSpan duration = cutEvent.EndTimeLocal!.Value - cutEvent.StartTimeLocal!.Value;

                            // Handle different views
                            if (this.schedule.CurrentView == View.Month || this.schedule.CurrentView == View.Agenda)
                            {
                                // In Month or Agenda view, maintain the original time but change the date
                                DateTime newStartDate = new DateTime(
                                    contextMenuCell.StartTime.Year,
                                    contextMenuCell.StartTime.Month,
                                    contextMenuCell.StartTime.Day,
                                    cutEvent.StartTimeLocal!.Value.Hour,
                                    cutEvent.StartTimeLocal!.Value.Minute,
                                    cutEvent.StartTimeLocal!.Value.Second);

                                cutEvent.StartTimeLocal = newStartDate;
                                cutEvent.EndTimeLocal = newStartDate.Add(duration);
                            }
                            else  //Αν η νέα ημερομηνία έχει ώρα γιατί είναι π.χ. από Day view.
                            {
                                cutEvent.StartTimeLocal = new DateTime(contextMenuCell.StartTime!.Year, contextMenuCell.StartTime!.Month, contextMenuCell.StartTime.Day, this.contextMenuCell.StartTime.Hour, this.contextMenuCell.StartTime.Minute, this.contextMenuCell.StartTime.Second);
                                cutEvent.EndTimeLocal = cutEvent.StartTimeLocal.Value.Add(duration);
                            }

                            // If the paste was in a different user's calendar, update the event user
                            if (this.contextMenuUser != null)
                            {
                                // Clear existing users and add the new one
                                cutEvent.EventUsers.Clear();
                                cutEvent.AddNewEventUser(this.contextMenuUser.UserId);
                            }
                            cutEvent.ObjectState = ObjectState.Modified;

                            // Saves the cut Event                           
                            EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                            await eventsWebApiClient.UpdateEvent(cutEvent, WebApi.Shared.Request.RecurrentEventHandlingType.Current, null);

                            await InvokeAsync(async () => { await LoadEvents(); });
                        }
                        else
                        {
                            this.toastService.ShowInfo(Resources.CalendarResource.NothingToPaste);
                        }

                        // Remove the copy & cut event from the cache
                        await cache.RemoveAsync("CopiedCalendarEvent");
                        await cache.RemoveAsync("CutCalendarEvent");
                        this.cutEventId = null;

                        break;
                }

                this.StateHasChanged();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task StoreCopiedEventInCache(Event eventToStore)
        {
            try
            {
                var cacheOptions = new HybridCacheEntryOptions
                {
                    LocalCacheExpiration = TimeSpan.FromHours(1) // Keep for 1 hour
                };

                await cache.SetAsync("CopiedCalendarEvent", eventToStore, cacheOptions);
                logger.LogInformation($"Stored event in cache: ID={eventToStore.EventId}");
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to store copied event in cache");
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
        }

        private async Task StoreCutEventInCache(Event eventToStore)
        {
            try
            {
                var cacheOptions = new HybridCacheEntryOptions
                {
                    LocalCacheExpiration = TimeSpan.FromHours(1) // Keep for 1 hour
                };

                await cache.SetAsync("CutCalendarEvent", eventToStore, cacheOptions);
                logger.LogInformation($"Stored event in cache: ID={eventToStore.EventId}");
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to store cut event in cache");
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
        }

        private async Task<Event?> RetrieveCopiedEventFromCache()
        {
            try
            {
                Event? cachedEvent = await cache.GetOrCreateAsync<Event?>("CopiedCalendarEvent", async cancel => await Task.FromResult<Event?>(null));

                logger.LogInformation($"Retrieved event from cache: {(cachedEvent == null ? "NULL" : $"ID={cachedEvent.EventId}")}");
                return cachedEvent;
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to store copied event in cache");
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                return null;
            }
        }

        private async Task<Event?> RetrieveCutEventFromCache()
        {
            try
            {
                Event? cachedEvent = await cache.GetOrCreateAsync<Event?>("CutCalendarEvent", async cancel => await Task.FromResult<Event?>(null));

                logger.LogInformation($"Retrieved event from cache: {(cachedEvent == null ? "NULL" : $"ID={cachedEvent.EventId}")}");
                return cachedEvent;
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to get cut event from cache");
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                return null;
            }
        }

        public async void PrintCalendar()
        {
            try
            {
                PrintOptions Options = new PrintOptions() { Height = "auto", Width = "auto" };
                await this.schedule.PrintAsync(Options);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to get cut event from cache");
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
        }

        public async Task ExportCalendarToExcel()
        {
            try
            {
                ExportOptions Options = new ExportOptions() { CustomData = this.events, ExportType = ExcelFormat.Xlsx, Fields = new string[] { "Id", "Subject", "StartTimeLocal", "EndTimeLocal", "AllDay", "Description", "EventState.Name", "Contact.FirstLastName"  } };
                await this.schedule.ExportToExcelAsync(Options);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to get cut event from cache");
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
        }
    }
}


