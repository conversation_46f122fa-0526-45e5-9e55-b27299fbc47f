﻿using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.WebApi.Shared.Request;

namespace FluentBlue.Application.Business
{
    public interface IEventsBusiness
    {
        Task<List<FluentBlue.Data.Model.DBOs.Calendar.Event>> GetEvents(Application.Business.Request.RequestEventsParameters parameters);
        Task<FluentBlue.Data.Model.DBOs.Calendar.Event?> GetEvent(Guid eventId);
        Task CreateOrUpdateNoRecurrentEvent(Event eventObj);
        Task CreateOrUpdateEvent(Event eventObj, RecurrentEventHandlingType? recurrentEventUpdateType, List<DateTime>? recurrenceDates);
        Task DeleteEvent(Guid eventId, RecurrentEventHandlingType? recurrentEventUpdateType);
        Task<List<Event>> GetAllEventsOfContact(Guid contactId);
        Task<Dictionary<User, int>> GetTotalUpcomingEventsForTomorrowByUser(Guid tenantId);
        Task SaveEventsBatch(List<Event> events);
        List<Event> GenerateRecurrentEvents(Event evnt, List<DateTime> recurrenceDates, string recurrenceRule, Guid recurrenceIdGuid);
        Task<int> GetFutureEventsCount(Guid tenantId);
        Task<int> GetTotalEventsForDate(Guid tenantId, DateTime currentDateLocal, string timeZoneId);
    }
}