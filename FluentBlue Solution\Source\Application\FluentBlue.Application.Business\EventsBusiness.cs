﻿using AutoMapper;
using Azure.Core;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.ComponentModel.DataAnnotations;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace FluentBlue.Application.Business
{
    public class EventsBusiness : IEventsBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public EventsBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, IMapper mapper, ILogger<EventsBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.Event>> GetEvents(Application.Business.Request.RequestEventsParameters parameters)
        {
            try
            {
                DateTime startDate = new DateTime(parameters.StartDateTicksUtc);
                DateTime endDate = new DateTime(parameters.EndDateTicksUtc);

                //Validation
                int resultsCount;
                parameters.Filter = (parameters.Filter ?? "").Trim();

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Calendar.Event> query = this.dbContext.Events.Include(x => x.EventUsers).Include(x => x.EventReminders).AsNoTracking().AsQueryable();
                query = query.Where(x => x.TenantId == parameters.TenantId);
                if (parameters.Filter != "")
                {
                    query = query.Where(x => x.Subject.Contains(parameters.Filter) || x.Description.Contains(parameters.Filter));
                }
                if (parameters.UserIds != null && parameters.UserIds.Count() > 0)
                {
                    query = query.Where(x => x.EventUsers.Any(eu => parameters.UserIds.Contains(eu.UserId!.Value)));
                }
                query = query.Where(x => (x.StartTimeUtc.Date >= startDate && x.StartTimeUtc.Date <= endDate) || (x.EndTimeUtc.Date >= startDate && x.EndTimeUtc.Date <= endDate) || (x.StartTimeUtc.Date < startDate && x.EndTimeUtc.Date > endDate));
                //Διαβάζει το σύνολο των records.
                resultsCount = query.Count();

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Calendar.Event> events = await query.AsNoTracking().ToListAsync();
                //List<FluentBlue.Data.Model.DTOs.EventView> contactsDTOs = mapper.Map<List<FluentBlue.Data.Model.DBOs.Events.Event>, List<FluentBlue.Data.Model.DTOs.EventView>>(events);

                //Response
                return events;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "Parameters=" + parameters.ToString() });
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.Event>> GetAllEventsOfContact(Guid contactId)
        {
            try
            {
                //Validation

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Calendar.Event> query = this.dbContext.Events.AsNoTracking().AsQueryable();
                query = query.Where(c => c.ContactId == contactId);

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Calendar.Event> events = await query.AsNoTracking().ToListAsync();

                //Response
                return events;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Calendar.Event?> GetEvent(Guid eventId)
        {
            try
            {
                //Query
                IQueryable<Event> query = dbContext.Events.Include(x => x.EventUsers).Include(x => x.EventReminders).AsQueryable();
                query = query.Where(c => c.EventId.ToString() == eventId.ToString());

                return await query.AsNoTracking().FirstOrDefaultAsync<Event>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "EventId=" + eventId.ToString() });
                throw;
            }
        }

        public async Task CreateOrUpdateNoRecurrentEvent(Event eventObj)
        {
            try
            {
                //Validation
                if (eventObj == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                EventValidator validator = new EventValidator();
                FluentValidation.Results.ValidationResult result = validator.Validate(eventObj);
                string validationErrors = string.Empty;
                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        validationErrors += failure.ErrorMessage + ". ";
                    }
                    throw new ApplicationException(validationErrors);
                }

                //Query
                this.dbContext.Attach(eventObj);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (DbUpdateConcurrencyException ex)
            {
                throw;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { eventObj.EventId });
                throw;
            }
        }

        public async Task CreateOrUpdateEvent(Event evnt, RecurrentEventHandlingType? recurrentEventUpdateType, List<DateTime>? recurrenceDates)
        {
            try
            {
                //Validation
                if (evnt == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                EventValidator validator = new EventValidator();
                FluentValidation.Results.ValidationResult result = validator.Validate(evnt);
                string validationErrors = string.Empty;
                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        validationErrors += failure.ErrorMessage + ". ";
                    }
                    throw new ApplicationException(validationErrors);
                }

                #region  Query
                //If we create a new Event
                if (evnt.ObjectState == Data.Model.ObjectState.Added)
                {
                    //If there is no recurrence.
                    if (evnt.CustomRecurrence == false)
                    {
                        await this.CreateOrUpdateNoRecurrentEvent(evnt);
                    }
                    else  //If there is recurrence.
                    {
                        List<Event> recurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());
                        await this.SaveEventsBatch(recurrenceEvents);
                    }
                }

                //If we update the Event.
                else if (evnt.ObjectState == Data.Model.ObjectState.Modified)
                {
                    Event? initialEvent = await this.GetEvent(evnt.EventId);

                    //If the event we are going to update it does not exists.
                    if (initialEvent == null)
                    {
                        throw new ApplicationException(Resources.GlobalResource.InvalidDataMessage);
                    }
                    else  //If the Event we are going to update exists in database.
                    {
                        //If the initial Event and the updated Event does not have recurrence
                        if (initialEvent.CustomRecurrence == false && evnt.CustomRecurrence == false)
                        {
                            await this.CreateOrUpdateNoRecurrentEvent(evnt);  //This is the most simple case.
                        }
                        //If the initial Event does not have recurrence but the new Event has a recurrence.
                        else if (initialEvent.CustomRecurrence == false && evnt.CustomRecurrence == true)
                        {
                            List<Event> recurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());

                            //We delete initial Event since we are going to create the recurrent events.
                            evnt.ObjectState = ObjectState.Deleted;
                            recurrenceEvents.Add(evnt);  //Adds current Event in the batch list of events, so as to be deleted.
                            await this.SaveEventsBatch(recurrenceEvents);  //Creates the new recurrent events and deletes the original one.
                        }
                        //If the initial Event had recurrence and the updated Event has exact the same rucurrence (recurrence is not changed).
                        else if (initialEvent.CustomRecurrence == true && evnt.CustomRecurrence == true && initialEvent.CustomRecurrenceRule == evnt.CustomRecurrenceRule)
                        {
                            //Depending of the user respond about the type of update.

                            //If user want to update only the current recurrence.
                            if (recurrentEventUpdateType == RecurrentEventHandlingType.Current)
                            {
                                await this.CreateOrUpdateNoRecurrentEvent(evnt);
                            }
                            else if (recurrentEventUpdateType == RecurrentEventHandlingType.CurrentAndFollowing)
                            {
                                //Finds the following events of recurrence.
                                List<Event> followingEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc > evnt.StartTimeUtc).AsNoTracking().ToList();

                                //Copies changes to data to following recurrent events.
                                foreach (Event followingEvent in followingEvents)
                                {
                                    followingEvent.ObjectState = ObjectState.Modified;
                                    followingEvent.UserTimeZoneId = evnt.UserTimeZoneId;
                                    followingEvent.AllDay = evnt.AllDay;
                                    followingEvent.EventCategoryId = evnt.EventCategoryId;
                                    followingEvent.Block = evnt.Block;
                                    followingEvent.ContactId = evnt.ContactId;
                                    followingEvent.DateModifiedUtc = DateTime.UtcNow;
                                    followingEvent.Description = evnt.Description;
                                    followingEvent.StartTimeUtc = new DateTime(followingEvent.StartTimeUtc.Year, followingEvent.StartTimeUtc.Month, followingEvent.StartTimeUtc.Day, evnt.StartTimeUtc.Hour, evnt.StartTimeUtc.Minute, followingEvent.StartTimeUtc.Second);
                                    followingEvent.EndTimeUtc = new DateTime(followingEvent.EndTimeUtc.Year, followingEvent.EndTimeUtc.Month, followingEvent.EndTimeUtc.Day, evnt.EndTimeUtc.Hour, evnt.EndTimeUtc.Minute, followingEvent.EndTimeUtc.Second);
                                    followingEvent.EventStateId = evnt.EventStateId;
                                    followingEvent.Location = evnt.Location;
                                    followingEvent.Subject = evnt.Subject;
                                    foreach (EventUser eventUser in evnt.EventUsers)  //Updates the EventUsers
                                    {
                                        if (eventUser.ObjectState == ObjectState.Added)
                                        {
                                            EventUser recurEventUser = followingEvent.AddNewEventUser(eventUser.UserId);
                                        }
                                        else if (eventUser.ObjectState == ObjectState.Deleted)
                                        {
                                            EventUser? tempEventUser = followingEvent.EventUsers.Where(x => x.UserId == eventUser.UserId).FirstOrDefault();
                                            if (tempEventUser != null)
                                            {
                                                tempEventUser.ObjectState = ObjectState.Deleted;
                                            }
                                        }
                                    }
                                }
                                followingEvents.Add(evnt);  //Adds also the current event so as to be updated with the following ones.

                                await this.SaveEventsBatch(followingEvents);
                            }
                            else if (recurrentEventUpdateType == RecurrentEventHandlingType.All)
                            {
                                //Finds all events of recurrence.
                                List<Event> recurrentEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId).AsNoTracking().ToList();

                                //Copies changes to data to following recurrent events.
                                foreach (Event recurrentEvent in recurrentEvents)
                                {
                                    recurrentEvent.ObjectState = ObjectState.Modified;
                                    recurrentEvent.UserTimeZoneId = evnt.UserTimeZoneId;
                                    recurrentEvent.AllDay = evnt.AllDay;
                                    recurrentEvent.EventCategoryId = evnt.EventCategoryId;
                                    recurrentEvent.Block = evnt.Block;
                                    recurrentEvent.ContactId = evnt.ContactId;
                                    recurrentEvent.DateModifiedUtc = DateTime.UtcNow;
                                    recurrentEvent.Description = evnt.Description;
                                    recurrentEvent.StartTimeUtc = new DateTime(recurrentEvent.StartTimeUtc.Year, recurrentEvent.StartTimeUtc.Month, recurrentEvent.StartTimeUtc.Day, evnt.StartTimeUtc.Hour, evnt.StartTimeUtc.Minute, recurrentEvent.StartTimeUtc.Second);
                                    recurrentEvent.EndTimeUtc = new DateTime(recurrentEvent.EndTimeUtc.Year, recurrentEvent.EndTimeUtc.Month, recurrentEvent.EndTimeUtc.Day, evnt.EndTimeUtc.Hour, evnt.EndTimeUtc.Minute, recurrentEvent.EndTimeUtc.Second);
                                    recurrentEvent.EventStateId = evnt.EventStateId;
                                    recurrentEvent.Location = evnt.Location;
                                    recurrentEvent.Subject = evnt.Subject;
                                    foreach (EventUser eventUser in evnt.EventUsers)  //Updates the EventUsers
                                    {
                                        if (eventUser.ObjectState == ObjectState.Added)
                                        {
                                            EventUser recurEventUser = recurrentEvent.AddNewEventUser(eventUser.UserId);
                                        }
                                        else if (eventUser.ObjectState == ObjectState.Deleted)
                                        {
                                            EventUser? tempEventUser = recurrentEvent.EventUsers.Where(x => x.UserId == eventUser.UserId).FirstOrDefault();
                                            if (tempEventUser != null)
                                            {
                                                tempEventUser.ObjectState = ObjectState.Deleted;
                                            }
                                        }
                                    }
                                }

                                await this.SaveEventsBatch(recurrentEvents);
                            }
                        }
                        //If the initial Event and the updated Event had recurrence and the rucurrence is changed.
                        else if (initialEvent.CustomRecurrence == true && evnt.CustomRecurrence == true && initialEvent.CustomRecurrenceRule != evnt.CustomRecurrenceRule)
                        {
                            //Depending of the user respond about the type of update.

                            //If user want to update only the current recurrence.
                            if (recurrentEventUpdateType == RecurrentEventHandlingType.Current)
                            {
                                await this.CreateOrUpdateNoRecurrentEvent(evnt);
                            }
                            else if (recurrentEventUpdateType == RecurrentEventHandlingType.CurrentAndFollowing)
                            {
                                //Explanation: Since recurrence is changed, current and following Events should be deleted and new ones (from now on) should be created (with different recurrence).

                                #region  Finds the Events that should be deleted.
                                //Finds the following events of recurrence.
                                List<Event> deleteEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc > evnt.StartTimeUtc).AsNoTracking().ToList();

                                //Copies changes to data to following recurrent events.
                                foreach (Event deleteEvent in deleteEvents)
                                {
                                    deleteEvent.ObjectState = ObjectState.Deleted;
                                    deleteEvent.UserTimeZoneId = evnt.UserTimeZoneId;  //Also set the UserTimeZoneId (not to create new loop).
                                }

                                evnt.ObjectState = ObjectState.Deleted;
                                deleteEvents.Add(evnt);  //Adds the current event for deletion.
                                #endregion

                                #region  Created the new "current" and "following" recurrent Events.
                                List<Event> recurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());
                                List<Event> newCurrentAndFollowingEvents = recurrenceEvents.Where(x => x.StartTimeUtc == DateTime.Today.ToUniversalTime()).ToList();  //Gets only the new recurrent events from today and following days.
                                #endregion

                                //Saves all Events (deleted or created).
                                List<Event> eventsForCreateUpdate = new List<Event>();
                                eventsForCreateUpdate.AddRange(deleteEvents);
                                eventsForCreateUpdate.AddRange(newCurrentAndFollowingEvents);

                                await this.SaveEventsBatch(eventsForCreateUpdate);
                            }
                            else if (recurrentEventUpdateType == RecurrentEventHandlingType.All)
                            {
                                //Explanation: Since recurrence is changed, all Events should be deleted and new ones should be created (with different recurrence).

                                #region  Finds the Events that should be deleted.
                                //Finds the following events of recurrence.
                                List<Event> deleteEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId).AsNoTracking().ToList();

                                //Copies changes to data to following recurrent events.
                                foreach (Event deleteEvent in deleteEvents)
                                {
                                    deleteEvent.ObjectState = ObjectState.Deleted;
                                    deleteEvent.UserTimeZoneId = evnt.UserTimeZoneId;  //Also set the UserTimeZoneId (not to create new loop).
                                }
                                #endregion

                                //Created the new "current" and "following" recurrent Events.
                                List<Event> newRecurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());

                                //Saves all Events (deleted or created).
                                List<Event> eventsForCreateUpdate = new List<Event>();
                                eventsForCreateUpdate.AddRange(deleteEvents);
                                eventsForCreateUpdate.AddRange(newRecurrenceEvents);

                                await this.SaveEventsBatch(eventsForCreateUpdate);
                            }
                        }
                    }
                }

                #endregion

                //Response
            }
            catch (DbUpdateConcurrencyException ex)
            {
                throw;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { evnt.EventId });
                throw;
            }
        }


        /// <summary>
        /// This function creates, updates and deletes Events.
        /// </summary>
        /// <param name="events"></param>
        /// <returns></returns>
        public async Task SaveEventsBatch(List<Event> events)
        {
            try
            {
                if (events == null || events.Count == 0)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                EventValidator validator = new EventValidator();
                List<string> validationErrors = new List<string>();

                foreach (var eventObj in events)
                {
                    FluentValidation.Results.ValidationResult result = validator.Validate(eventObj);
                    if (!result.IsValid)
                    {
                        foreach (var failure in result.Errors)
                        {
                            validationErrors.Add(failure.ErrorMessage);
                        }
                    }
                    else
                    {
                        this.dbContext.Attach(eventObj);
                    }
                }

                if (validationErrors.Count > 0)
                {
                    throw new ApplicationException(string.Join(". ", validationErrors));
                }

                await this.dbContext.SaveChangesAsync();
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message);
                throw;
            }
        }

        public async Task DeleteEvent(Guid eventId, RecurrentEventHandlingType? recurrentEventUpdateType)
        {
            Event? evnt = await this.dbContext.Events.AsNoTracking().Where(x => x.EventId == eventId).FirstAsync();
            if (evnt != null)
            {
                //Αν δεν έχει δοθεί τιμή στο παράμετρο recurrentEventUpdateType, τότε θεωρείται ότι θέλουμε να διαγράψουμε το συγκεκριμένο Event.
                //Ή αν έχει δοθεί τιμή Current, τότε θα διαγραφεί μόνο το συγκεκριμένο Event.
                if (recurrentEventUpdateType == null || (recurrentEventUpdateType != null && recurrentEventUpdateType == RecurrentEventHandlingType.Current))
                {
                    evnt.ObjectState = ObjectState.Deleted;
                    this.dbContext.Attach(evnt);
                    this.dbContext.SaveChanges();
                }
                else if (recurrentEventUpdateType != null && recurrentEventUpdateType == RecurrentEventHandlingType.CurrentAndFollowing)
                {

                    //Finds the following events of recurrence.
                    List<Event> followingEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc > evnt.StartTimeUtc).AsNoTracking().ToList();

                    //Copies changes to data to following recurrent events.
                    foreach (Event followingEvent in followingEvents)
                    {
                        followingEvent.ObjectState = ObjectState.Deleted;
                    }
                    followingEvents.Add(evnt);  //Adds also the current event so as to be deleted with the following ones.

                    await this.SaveEventsBatch(followingEvents);
                }
                else if (recurrentEventUpdateType != null && recurrentEventUpdateType == RecurrentEventHandlingType.All)
                {
                    //Finds the following events of recurrence.
                    List<Event> deleteEvents = this.dbContext.Events.Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId).AsNoTracking().ToList();

                    //Copies changes to data to following recurrent events.
                    foreach (Event deleteEvent in deleteEvents)
                    {
                        deleteEvent.ObjectState = ObjectState.Deleted;
                    }
                }
            }
            else
            {
                throw new ApplicationException(Resources.GlobalResource.InvalidDataMessage);
            }
        }

        public async Task<Dictionary<User, int>> GetTotalUpcomingEventsForTomorrowByUser(Guid tenantId)
        {
            try
            {
                DateTime tomorrow = DateTime.UtcNow.AddDays(1).Date;

                var users = await dbContext.Users
                    .Where(u => u.AvailableInEventUsers && u.TenantId == tenantId)
                    .AsNoTracking().ToListAsync();

                var events = await dbContext.Events
                    .Include(e => e.EventUsers)
                    .Where(e => e.StartTimeUtc.Date == tomorrow && e.TenantId == tenantId)
                    .AsNoTracking().ToListAsync();

                var groupedEvents = events
                    .SelectMany(e => e.EventUsers.Select(u => new { UserId = u.UserId, Event = e }))
                    .GroupBy(x => x.UserId)
                    .ToDictionary(g => users.First(u => u.UserId == g.Key), g => g.Select(x => x.Event).Count());

                return groupedEvents;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }

        public Dictionary<DateTime, int> GetTotalEventsPerDayForCurrentMonth(string tenantId)
        {
            var currentMonth = DateTime.UtcNow.Month;
            var currentYear = DateTime.UtcNow.Year;

            var eventsPerDay = dbContext.Events
                .Where(e => e.StartTimeUtc.Month == currentMonth && e.StartTimeUtc.Year == currentYear)
                .GroupBy(e => e.StartTimeUtc.Date)
                .ToDictionary(g => g.Key, g => g.Count());

            return eventsPerDay;
        }

        public List<Event> GenerateRecurrentEvents(Event evnt, List<DateTime> recurrenceDates, string recurrenceRule, Guid recurrenceIdGuid)
        {
            var recurrentEvents = new List<Event>();

            foreach (DateTime recurrenceDate in recurrenceDates)
            {
                //Event recurrentEvent = evnt.Clone();
                Event recurrentEvent = new Event();
                recurrentEvent.EventId = Guid.CreateVersion7();
                recurrentEvent.TenantId = evnt.TenantId;
                foreach (EventUser eventUser in evnt.EventUsers)
                {
                    //Explanation: Here the EventUsers list of each cloned Event, is exactly the same as the EventUsers of the initial Event. So, we should set new Guids and ObjectState to Added.
                    EventUser recurEventUser = recurrentEvent.AddNewEventUser(eventUser.UserId);
                }
                recurrentEvent.ObjectState = ObjectState.Added;
                recurrentEvent.AllDay = evnt.AllDay;
                recurrentEvent.Block = evnt.Block;
                recurrentEvent.ContactId = evnt.ContactId;
                recurrentEvent.UserTimeZoneId = evnt.UserTimeZoneId;
                recurrentEvent.Subject = evnt.Subject;
                recurrentEvent.Description = evnt.Description;
                recurrentEvent.EventCategoryId = evnt.EventCategoryId;
                recurrentEvent.Location = evnt.Location;
                recurrentEvent.DateCreatedUtc = DateTime.UtcNow;
                recurrentEvent.DateModifiedUtc = DateTime.UtcNow;
                recurrentEvent.StartTimeLocal = new DateTime(recurrenceDate.Year, recurrenceDate.Month, recurrenceDate.Day, evnt!.StartTimeLocal!.Value.Hour, evnt.StartTimeLocal!.Value.Minute, evnt!.StartTimeLocal!.Value.Second);
                recurrentEvent.EndTimeLocal = new DateTime(recurrenceDate.Year, recurrenceDate.Month, recurrenceDate.Day, evnt.EndTimeLocal!.Value.Hour, evnt.EndTimeLocal!.Value.Minute, evnt.EndTimeLocal!.Value.Second);
                recurrentEvent.StartTimeZone = evnt.StartTimeZone;
                recurrentEvent.EndTimeZone = evnt.EndTimeZone;
                recurrentEvent.CustomRecurrence = true;
                recurrentEvent.CustomRecurrenceId = recurrenceIdGuid;
                recurrentEvent.CustomRecurrenceRule = recurrenceRule;
                recurrentEvents.Add(recurrentEvent);
            }

            return recurrentEvents;
        }

        public async Task<int> GetFutureEventsCount(Guid tenantId)
        {
            try
            {
                // Query
                IQueryable<Event> query = this.dbContext.Events.AsNoTracking().AsQueryable();
                query = query.Where(c => c.StartTimeUtc > DateTime.UtcNow && c.TenantId == tenantId);

                // Return total count
                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }


        public async Task<int> GetTotalEventsForDate(Guid tenantId, DateTime currentDateLocal, string timeZoneId)
        {
            try
            {
                // Convert local date to UTC  
                DateTime startOfDayUtc = TimeZoneInfo.ConvertTimeToUtc(currentDateLocal.Date, TimeZoneInfo.FindSystemTimeZoneById(timeZoneId));
                DateTime endOfDayUtc = startOfDayUtc.AddDays(1).AddTicks(-1);

                // Query  
                IQueryable<Event> query = this.dbContext.Events.AsNoTracking().AsQueryable();
                query = query.Where(e => e.TenantId == tenantId && e.StartTimeUtc >= startOfDayUtc && e.StartTimeUtc <= endOfDayUtc);

                // Return total count  
                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString(), "CurrentDateLocal=" + currentDateLocal.ToString() });
                throw;
            }
        }
    }
}
